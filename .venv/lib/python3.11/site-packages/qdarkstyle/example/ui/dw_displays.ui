<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DockWidget</class>
 <widget class="QDockWidget" name="DockWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>592</width>
    <height>557</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Displays</string>
  </property>
  <widget class="QWidget" name="dockWidgetContents">
   <layout class="QGridLayout" name="gridLayout">
    <item row="2" column="1">
     <widget class="QTextBrowser" name="textBrowser">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="toolTip">
       <string>This is a tool tip</string>
      </property>
      <property name="statusTip">
       <string>This is a status tip</string>
      </property>
      <property name="whatsThis">
       <string>This is &quot;what is this&quot;</string>
      </property>
      <property name="html">
       <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'Ubuntu'; font-size:11pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-family:'Cantarell';&quot;&gt;Testing&lt;/span&gt;&lt;/p&gt;
&lt;p style=&quot;-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px; font-family:'Cantarell';&quot;&gt;&lt;br /&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
      </property>
     </widget>
    </item>
    <item row="0" column="1">
     <widget class="QLabel" name="label_77">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="font">
       <font>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>Enabled</string>
      </property>
     </widget>
    </item>
    <item row="0" column="2">
     <widget class="QLabel" name="label_78">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="font">
       <font>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>Disabled</string>
      </property>
     </widget>
    </item>
    <item row="1" column="0">
     <widget class="QLabel" name="label_3">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="font">
       <font>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="toolTip">
       <string>This is a tool tip</string>
      </property>
      <property name="statusTip">
       <string>This is a status tip</string>
      </property>
      <property name="whatsThis">
       <string>This is &quot;what is this&quot;</string>
      </property>
      <property name="text">
       <string>Label</string>
      </property>
     </widget>
    </item>
    <item row="1" column="2">
     <widget class="QLabel" name="label_79">
      <property name="enabled">
       <bool>false</bool>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="text">
       <string>Testing</string>
      </property>
     </widget>
    </item>
    <item row="2" column="0">
     <widget class="QLabel" name="label_4">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="font">
       <font>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="toolTip">
       <string>This is a tool tip</string>
      </property>
      <property name="statusTip">
       <string>This is a status tip</string>
      </property>
      <property name="whatsThis">
       <string>This is &quot;what is this&quot;</string>
      </property>
      <property name="text">
       <string>TextBrowser</string>
      </property>
     </widget>
    </item>
    <item row="2" column="2">
     <widget class="QTextBrowser" name="textBrowserDis">
      <property name="enabled">
       <bool>false</bool>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="html">
       <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'Ubuntu'; font-size:11pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-family:'Cantarell';&quot;&gt;Testing&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
      </property>
     </widget>
    </item>
    <item row="3" column="0">
     <widget class="QLabel" name="label_5">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="font">
       <font>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="toolTip">
       <string>This is a tool tip</string>
      </property>
      <property name="statusTip">
       <string>This is a status tip</string>
      </property>
      <property name="whatsThis">
       <string>This is &quot;what is this&quot;</string>
      </property>
      <property name="text">
       <string>GraphicsView</string>
      </property>
     </widget>
    </item>
    <item row="3" column="1">
     <widget class="QGraphicsView" name="graphicsView">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="toolTip">
       <string>This is a tool tip</string>
      </property>
      <property name="statusTip">
       <string>This is a status tip</string>
      </property>
      <property name="whatsThis">
       <string>This is &quot;what is this&quot;</string>
      </property>
     </widget>
    </item>
    <item row="3" column="2">
     <widget class="QGraphicsView" name="graphicsViewDis">
      <property name="enabled">
       <bool>false</bool>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
     </widget>
    </item>
    <item row="4" column="0">
     <widget class="QLabel" name="label_6">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="font">
       <font>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="toolTip">
       <string>This is a tool tip</string>
      </property>
      <property name="statusTip">
       <string>This is a status tip</string>
      </property>
      <property name="whatsThis">
       <string>This is &quot;what is this&quot;</string>
      </property>
      <property name="text">
       <string>CalendarWidget</string>
      </property>
     </widget>
    </item>
    <item row="4" column="1">
     <widget class="QCalendarWidget" name="calendarWidget">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>350</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="font">
       <font>
        <family>Lato</family>
        <pointsize>8</pointsize>
        <italic>false</italic>
       </font>
      </property>
      <property name="toolTip">
       <string>This is a tool tip</string>
      </property>
      <property name="statusTip">
       <string>This is a status tip</string>
      </property>
      <property name="whatsThis">
       <string>This is &quot;what is this&quot;</string>
      </property>
     </widget>
    </item>
    <item row="5" column="2">
     <widget class="QLCDNumber" name="lcdNumberDis">
      <property name="enabled">
       <bool>false</bool>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
     </widget>
    </item>
    <item row="5" column="0">
     <widget class="QLabel" name="label_7">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="font">
       <font>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="toolTip">
       <string>This is a tool tip</string>
      </property>
      <property name="statusTip">
       <string>This is a status tip</string>
      </property>
      <property name="whatsThis">
       <string>This is &quot;what is this&quot;</string>
      </property>
      <property name="text">
       <string>LCDNumber</string>
      </property>
     </widget>
    </item>
    <item row="5" column="1">
     <widget class="QLCDNumber" name="lcdNumber">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="toolTip">
       <string>This is a tool tip</string>
      </property>
      <property name="statusTip">
       <string>This is a status tip</string>
      </property>
      <property name="whatsThis">
       <string>This is &quot;what is this&quot;</string>
      </property>
     </widget>
    </item>
    <item row="6" column="0">
     <widget class="QLabel" name="label_8">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="font">
       <font>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="toolTip">
       <string>This is a tool tip</string>
      </property>
      <property name="statusTip">
       <string>This is a status tip</string>
      </property>
      <property name="whatsThis">
       <string>This is &quot;what is this&quot;</string>
      </property>
      <property name="text">
       <string>ProgressBar</string>
      </property>
     </widget>
    </item>
    <item row="6" column="1">
     <widget class="QProgressBar" name="progressBar">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="toolTip">
       <string>This is a tool tip</string>
      </property>
      <property name="statusTip">
       <string>This is a status tip</string>
      </property>
      <property name="whatsThis">
       <string>This is &quot;what is this&quot;</string>
      </property>
      <property name="value">
       <number>24</number>
      </property>
     </widget>
    </item>
    <item row="6" column="2">
     <widget class="QProgressBar" name="progressBarDis">
      <property name="enabled">
       <bool>false</bool>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="value">
       <number>24</number>
      </property>
     </widget>
    </item>
    <item row="7" column="0">
     <widget class="QLabel" name="label_9">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="font">
       <font>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="toolTip">
       <string>This is a tool tip</string>
      </property>
      <property name="statusTip">
       <string>This is a status tip</string>
      </property>
      <property name="whatsThis">
       <string>This is &quot;what is this&quot;</string>
      </property>
      <property name="text">
       <string>Line - H</string>
      </property>
     </widget>
    </item>
    <item row="7" column="1">
     <widget class="Line" name="lineH">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="toolTip">
       <string>This is a tool tip</string>
      </property>
      <property name="statusTip">
       <string>This is a status tip</string>
      </property>
      <property name="whatsThis">
       <string>This is &quot;what is this&quot;</string>
      </property>
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
     </widget>
    </item>
    <item row="7" column="2">
     <widget class="Line" name="lineHDis">
      <property name="enabled">
       <bool>false</bool>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
     </widget>
    </item>
    <item row="8" column="0">
     <widget class="QLabel" name="label_10">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="font">
       <font>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="toolTip">
       <string>This is a tool tip</string>
      </property>
      <property name="statusTip">
       <string>This is a status tip</string>
      </property>
      <property name="whatsThis">
       <string>This is &quot;what is this&quot;</string>
      </property>
      <property name="text">
       <string>Line - V</string>
      </property>
     </widget>
    </item>
    <item row="8" column="1">
     <widget class="Line" name="lineV">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>50</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="toolTip">
       <string>This is a tool tip</string>
      </property>
      <property name="statusTip">
       <string>This is a status tip</string>
      </property>
      <property name="whatsThis">
       <string>This is &quot;what is this&quot;</string>
      </property>
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
     </widget>
    </item>
    <item row="8" column="2">
     <widget class="Line" name="lineVDis">
      <property name="enabled">
       <bool>false</bool>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>50</height>
       </size>
      </property>
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
     </widget>
    </item>
    <item row="9" column="0">
     <spacer name="verticalSpacer">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="toolTip">
       <string>This is a tool tip</string>
      </property>
      <property name="statusTip">
       <string>This is a status tip</string>
      </property>
      <property name="whatsThis">
       <string>This is &quot;what is this&quot;</string>
      </property>
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="10" column="0" colspan="3">
     <widget class="QLabel" name="label_37">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="toolTip">
       <string>This is a tool tip</string>
      </property>
      <property name="statusTip">
       <string>This is a status tip</string>
      </property>
      <property name="whatsThis">
       <string>This is &quot;what is this&quot;</string>
      </property>
      <property name="text">
       <string>Inside DockWidget</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
    </item>
    <item row="1" column="1">
     <widget class="QLabel" name="label_2">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="toolTip">
       <string>This is a tool tip</string>
      </property>
      <property name="statusTip">
       <string>This is a status tip</string>
      </property>
      <property name="whatsThis">
       <string>This is &quot;what is this&quot;</string>
      </property>
      <property name="text">
       <string>Testing</string>
      </property>
     </widget>
    </item>
    <item row="4" column="2">
     <widget class="QCalendarWidget" name="calendarWidgetDis">
      <property name="enabled">
       <bool>false</bool>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="maximumSize">
       <size>
        <width>350</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="font">
       <font>
        <family>Lato</family>
        <pointsize>8</pointsize>
        <italic>false</italic>
       </font>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>progressBar</sender>
   <signal>valueChanged(int)</signal>
   <receiver>progressBarDis</receiver>
   <slot>setValue(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>300</x>
     <y>496</y>
    </hint>
    <hint type="destinationlabel">
     <x>469</x>
     <y>497</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>calendarWidget</sender>
   <signal>currentPageChanged(int,int)</signal>
   <receiver>calendarWidgetDis</receiver>
   <slot>setCurrentPage(int,int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>254</x>
     <y>321</y>
    </hint>
    <hint type="destinationlabel">
     <x>485</x>
     <y>313</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>calendarWidget</sender>
   <signal>clicked(QDate)</signal>
   <receiver>calendarWidgetDis</receiver>
   <slot>setSelectedDate(QDate)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>275</x>
     <y>354</y>
    </hint>
    <hint type="destinationlabel">
     <x>465</x>
     <y>359</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
