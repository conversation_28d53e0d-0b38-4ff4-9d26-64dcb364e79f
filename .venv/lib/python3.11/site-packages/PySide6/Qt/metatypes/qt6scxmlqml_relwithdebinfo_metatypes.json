[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QScxmlEvent"}, {"name": "QML.AddedInVersion", "value": "1288"}], "className": "QScxmlEventForeign", "gadget": true, "lineNumber": 31, "qualifiedClassName": "QScxmlEventForeign"}, {"classInfos": [{"name": "QML.Element", "value": "EventConnection"}, {"name": "QML.AddedInVersion", "value": "1288"}], "className": "QScxmlEventConnection", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 39, "object": true, "properties": [{"bindable": "bindableEvents", "constant": false, "designable": true, "final": false, "index": 0, "name": "events", "notify": "eventsChanged", "read": "events", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setEvents"}, {"bindable": "bindableStateMachine", "constant": false, "designable": true, "final": false, "index": 1, "name": "stateMachine", "notify": "stateMachineChanged", "read": "stateMachine", "required": false, "scriptable": true, "stored": true, "type": "QScxmlStateMachine*", "user": false, "write": "setStateMachine"}], "qualifiedClassName": "QScxmlEventConnection", "signals": [{"access": "public", "index": 0, "name": "eventsChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "stateMachineChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "QScxmlEvent"}], "index": 2, "name": "occurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "eventconnection_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "qml<PERSON><PERSON><PERSON>n"}, {"name": "QML.Element", "value": "InvokedServices"}, {"name": "QML.AddedInVersion", "value": "1288"}], "className": "QScxmlInvokedServices", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 29, "object": true, "properties": [{"bindable": "bindableStateMachine", "constant": false, "designable": true, "final": false, "index": 0, "name": "stateMachine", "notify": "stateMachineChanged", "read": "stateMachine", "required": false, "scriptable": true, "stored": true, "type": "QScxmlStateMachine*", "user": false, "write": "setStateMachine"}, {"bindable": "bindableChildren", "constant": false, "designable": true, "final": false, "index": 1, "name": "children", "notify": "childrenChanged", "read": "children", "required": false, "scriptable": true, "stored": true, "type": "QVariantMap", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "qml<PERSON><PERSON><PERSON>n", "read": "qml<PERSON><PERSON><PERSON>n", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}], "qualifiedClassName": "QScxmlInvokedServices", "signals": [{"access": "public", "index": 0, "name": "childrenChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "stateMachineChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "invokedservices_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "children"}], "className": "QScxmlStateMachineExtended", "lineNumber": 28, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "children", "read": "children", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}], "qualifiedClassName": "QScxmlStateMachineExtended", "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Only created through derived types"}, {"name": "QML.Element", "value": "StateMachine"}, {"name": "QML.Foreign", "value": "QScxmlStateMachine"}, {"name": "QML.Extended", "value": "QScxmlStateMachineExtended"}, {"name": "QML.AddedInVersion", "value": "1288"}], "className": "QScxmlStateMachineForeign", "gadget": true, "lineNumber": 42, "qualifiedClassName": "QScxmlStateMachineForeign"}], "inputFile": "statemachineextended_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "StateMachineLoader"}, {"name": "QML.AddedInVersion", "value": "1288"}], "className": "QScxmlStateMachineLoader", "lineNumber": 28, "object": true, "properties": [{"bindable": "bindableSource", "constant": false, "designable": true, "final": false, "index": 0, "name": "source", "notify": "sourceChanged", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}, {"bindable": "bindableStateMachine", "constant": false, "designable": false, "final": false, "index": 1, "name": "stateMachine", "notify": "stateMachineChanged", "read": "stateMachine", "required": false, "scriptable": true, "stored": true, "type": "QScxmlStateMachine*", "user": false}, {"bindable": "bindableInitialValues", "constant": false, "designable": true, "final": false, "index": 2, "name": "initialValues", "notify": "initialValuesChanged", "read": "initialValues", "required": false, "scriptable": true, "stored": true, "type": "QVariantMap", "user": false, "write": "setInitialValues"}, {"bindable": "bindableDataModel", "constant": false, "designable": true, "final": false, "index": 3, "name": "dataModel", "notify": "dataModelChanged", "read": "dataModel", "required": false, "scriptable": true, "stored": true, "type": "QScxmlDataModel*", "user": false, "write": "setDataModel"}], "qualifiedClassName": "QScxmlStateMachineLoader", "signals": [{"access": "public", "index": 0, "name": "sourceChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "initialValuesChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "stateMachineChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "dataModelChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "statemachineloader_p.h", "outputRevision": 68}]