[{"classes": [{"className": "LECBManagerNotifier", "lineNumber": 36, "object": true, "qualifiedClassName": "DarwinBluetooth::LECBManagerNotifier", "signals": [{"access": "public", "arguments": [{"name": "deviceInfo", "type": "QBluetoothDeviceInfo"}], "index": 0, "name": "deviceDiscovered", "returnType": "void"}, {"access": "public", "index": 1, "name": "discoveryFinished", "returnType": "void"}, {"access": "public", "index": 2, "name": "connected", "returnType": "void"}, {"access": "public", "index": 3, "name": "disconnected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "newValue", "type": "int"}], "index": 4, "name": "mtuChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "serviceDiscoveryFinished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "service", "type": "QSharedPointer<QLowEnergyServicePrivate>"}], "index": 6, "name": "serviceDetailsDiscoveryFinished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "char<PERSON><PERSON><PERSON>", "type": "QLowEnergyHandle"}, {"name": "value", "type": "QByteArray"}], "index": 7, "name": "characteristicRead", "returnType": "void"}, {"access": "public", "arguments": [{"name": "char<PERSON><PERSON><PERSON>", "type": "QLowEnergyHandle"}, {"name": "value", "type": "QByteArray"}], "index": 8, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "char<PERSON><PERSON><PERSON>", "type": "QLowEnergyHandle"}, {"name": "value", "type": "QByteArray"}], "index": 9, "name": "characteristicUpdated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "QLowEnergyHandle"}, {"name": "value", "type": "QByteArray"}], "index": 10, "name": "descriptorRead", "returnType": "void"}, {"access": "public", "arguments": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "QLowEnergyHandle"}, {"name": "value", "type": "QByteArray"}], "index": 11, "name": "descriptor<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "char<PERSON><PERSON><PERSON>", "type": "QLowEnergyHandle"}, {"name": "enabled", "type": "bool"}], "index": 12, "name": "notificationEnabled", "returnType": "void"}, {"access": "public", "index": 13, "name": "servicesWereModified", "returnType": "void"}, {"access": "public", "arguments": [{"name": "newValue", "type": "qint16"}], "index": 14, "name": "rssiUpdated", "returnType": "void"}, {"access": "public", "index": 15, "name": "LEnotSupported", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QBluetoothDeviceDiscoveryAgent::Error"}], "index": 16, "name": "CBManagerError", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QLowEnergyController::<PERSON><PERSON><PERSON>"}], "index": 17, "name": "CBManagerError", "returnType": "void"}, {"access": "public", "arguments": [{"name": "serviceUuid", "type": "QBluetoothUuid"}, {"name": "error", "type": "QLowEnergyController::<PERSON><PERSON><PERSON>"}], "index": 18, "name": "CBManagerError", "returnType": "void"}, {"access": "public", "arguments": [{"name": "serviceUuid", "type": "QBluetoothUuid"}, {"name": "error", "type": "QLowEnergyService::ServiceError"}], "index": 19, "name": "CBManagerError", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "btnotifier_p.h", "outputRevision": 68}, {"classes": [{"className": "QBluetoothDeviceDiscoveryAgentPrivate", "lineNumber": 67, "object": true, "qualifiedClassName": "QBluetoothDeviceDiscoveryAgentPrivate", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "DarwinBluetooth::DeviceInquiryDelegate"}]}], "inputFile": "qbluetoothdevicediscoveryagent_p.h", "outputRevision": 68}, {"classes": [{"className": "QBluetoothServiceInfoPrivate", "lineNumber": 54, "object": true, "qualifiedClassName": "QBluetoothServiceInfoPrivate", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbluetoothserviceinfo_p.h", "outputRevision": 68}, {"classes": [{"className": "QLowEnergyControllerPrivateDarwin", "lineNumber": 35, "object": true, "qualifiedClassName": "QLowEnergyControllerPrivateDarwin", "slots": [{"access": "private", "index": 0, "name": "_q_connected", "returnType": "void"}, {"access": "private", "index": 1, "name": "_q_disconnected", "returnType": "void"}, {"access": "private", "arguments": [{"name": "newValue", "type": "int"}], "index": 2, "name": "_q_mtu<PERSON><PERSON>ed", "returnType": "void"}, {"access": "private", "index": 3, "name": "_q_serviceDiscoveryFinished", "returnType": "void"}, {"access": "private", "arguments": [{"name": "service", "type": "QSharedPointer<QLowEnergyServicePrivate>"}], "index": 4, "name": "_q_serviceDetailsDiscoveryFinished", "returnType": "void"}, {"access": "private", "index": 5, "name": "_q_servicesWereModified", "returnType": "void"}, {"access": "private", "arguments": [{"name": "char<PERSON><PERSON><PERSON>", "type": "QLowEnergyHandle"}, {"name": "value", "type": "QByteArray"}], "index": 6, "name": "_q_characteristicRead", "returnType": "void"}, {"access": "private", "arguments": [{"name": "char<PERSON><PERSON><PERSON>", "type": "QLowEnergyHandle"}, {"name": "value", "type": "QByteArray"}], "index": 7, "name": "_q_<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "private", "arguments": [{"name": "char<PERSON><PERSON><PERSON>", "type": "QLowEnergyHandle"}, {"name": "value", "type": "QByteArray"}], "index": 8, "name": "_q_characteristicUpdated", "returnType": "void"}, {"access": "private", "arguments": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "QLowEnergyHandle"}, {"name": "value", "type": "QByteArray"}], "index": 9, "name": "_q_descriptorRead", "returnType": "void"}, {"access": "private", "arguments": [{"name": "char<PERSON><PERSON><PERSON>", "type": "QLowEnergyHandle"}, {"name": "value", "type": "QByteArray"}], "index": 10, "name": "_q_descriptor<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "private", "arguments": [{"name": "char<PERSON><PERSON><PERSON>", "type": "QLowEnergyHandle"}, {"name": "enabled", "type": "bool"}], "index": 11, "name": "_q_notificationEnabled", "returnType": "void"}, {"access": "private", "index": 12, "name": "_q_LEnotSupported", "returnType": "void"}, {"access": "private", "arguments": [{"name": "error", "type": "QLowEnergyController::<PERSON><PERSON><PERSON>"}], "index": 13, "name": "_q_CBManagerError", "returnType": "void"}, {"access": "private", "arguments": [{"name": "serviceUuid", "type": "QBluetoothUuid"}, {"name": "error", "type": "QLowEnergyController::<PERSON><PERSON><PERSON>"}], "index": 14, "name": "_q_CBManagerError", "returnType": "void"}, {"access": "private", "arguments": [{"name": "serviceUuid", "type": "QBluetoothUuid"}, {"name": "error", "type": "QLowEnergyService::ServiceError"}], "index": 15, "name": "_q_CBManagerError", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QLowEnergyControllerPrivate"}]}], "inputFile": "qlowenergycontroller_darwin_p.h", "outputRevision": 68}, {"classes": [{"className": "QBluetooth", "enums": [{"isClass": true, "isFlag": false, "name": "Security", "values": ["NoSecurity", "Authorization", "Authentication", "Encryption", "Secure"]}, {"isClass": true, "isFlag": false, "name": "AttAccessConstraint", "values": ["AttAuthorizationRequired", "AttAuthenticationRequired", "AttEncryptionRequired"]}], "lineNumber": 14, "namespace": true, "qualifiedClassName": "QBluetooth"}], "inputFile": "qbluetooth.h", "outputRevision": 68}, {"classes": [{"className": "QBluetoothDeviceDiscoveryAgent", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "InputOutputError", "PoweredOffError", "InvalidBluetoothAdapterError", "UnsupportedPlatformError", "UnsupportedDiscoveryMethod", "LocationServiceTurnedOffError", "MissingPermissionsError", "UnknownE<PERSON>r"]}, {"alias": "DiscoveryMethod", "isClass": false, "isFlag": true, "name": "DiscoveryMethods", "values": ["NoMethod", "ClassicMethod", "LowEnergyMethod"]}], "lineNumber": 18, "object": true, "qualifiedClassName": "QBluetoothDeviceDiscoveryAgent", "signals": [{"access": "public", "arguments": [{"name": "info", "type": "QBluetoothDeviceInfo"}], "index": 0, "name": "deviceDiscovered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "info", "type": "QBluetoothDeviceInfo"}, {"name": "<PERSON><PERSON><PERSON>s", "type": "QBluetoothDeviceInfo::Fields"}], "index": 1, "name": "deviceUpdated", "returnType": "void"}, {"access": "public", "index": 2, "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QBluetoothDeviceDiscoveryAgent::Error"}], "index": 3, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "index": 4, "name": "canceled", "returnType": "void"}], "slots": [{"access": "public", "index": 5, "name": "start", "returnType": "void"}, {"access": "public", "arguments": [{"name": "method", "type": "DiscoveryMethods"}], "index": 6, "name": "start", "returnType": "void"}, {"access": "public", "index": 7, "name": "stop", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbluetoothdevicediscoveryagent.h", "outputRevision": 68}, {"classes": [{"className": "QBluetoothLocalDevice", "enums": [{"isClass": false, "isFlag": false, "name": "Pairing", "values": ["Unpaired", "Paired", "AuthorizedPaired"]}, {"isClass": false, "isFlag": false, "name": "HostMode", "values": ["HostPoweredOff", "HostConnectable", "HostDiscoverable", "HostDiscoverableLimitedInquiry"]}, {"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "PairingError", "MissingPermissionsError", "UnknownE<PERSON>r"]}], "lineNumber": 19, "object": true, "qualifiedClassName": "QBluetoothLocalDevice", "signals": [{"access": "public", "arguments": [{"name": "state", "type": "QBluetoothLocalDevice::HostMode"}], "index": 0, "name": "hostModeStateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "address", "type": "QBluetoothAddress"}], "index": 1, "name": "deviceConnected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "address", "type": "QBluetoothAddress"}], "index": 2, "name": "deviceDisconnected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "address", "type": "QBluetoothAddress"}, {"name": "pairing", "type": "QBluetoothLocalDevice::Pairing"}], "index": 3, "name": "pairingFinished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QBluetoothLocalDevice::Error"}], "index": 4, "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbluetoothlocaldevice.h", "outputRevision": 68}, {"classes": [{"className": "QBluetoothServer", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "UnknownE<PERSON>r", "PoweredOffError", "InputOutputError", "ServiceAlreadyRegisteredError", "UnsupportedProtocolError", "MissingPermissionsError"]}], "lineNumber": 22, "object": true, "qualifiedClassName": "QBluetoothServer", "signals": [{"access": "public", "index": 0, "name": "newConnection", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QBluetoothServer::Error"}], "index": 1, "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbluetoothserver.h", "outputRevision": 68}, {"classes": [{"className": "QBluetoothServiceDiscoveryAgent", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "InputOutputError", "PoweredOffError", "InvalidBluetoothAdapterError", "MissingPermissionsError", "UnknownE<PERSON>r"]}, {"isClass": false, "isFlag": false, "name": "DiscoveryMode", "values": ["MinimalDiscovery", "FullDiscovery"]}], "lineNumber": 25, "object": true, "qualifiedClassName": "QBluetoothServiceDiscoveryAgent", "signals": [{"access": "public", "arguments": [{"name": "info", "type": "QBluetoothServiceInfo"}], "index": 0, "name": "serviceDiscovered", "returnType": "void"}, {"access": "public", "index": 1, "name": "finished", "returnType": "void"}, {"access": "public", "index": 2, "name": "canceled", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QBluetoothServiceDiscoveryAgent::Error"}], "index": 3, "name": "errorOccurred", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "mode", "type": "DiscoveryMode"}], "index": 4, "name": "start", "returnType": "void"}, {"access": "public", "index": 5, "isCloned": true, "name": "start", "returnType": "void"}, {"access": "public", "index": 6, "name": "stop", "returnType": "void"}, {"access": "public", "index": 7, "name": "clear", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbluetoothservicediscoveryagent.h", "outputRevision": 68}, {"classes": [{"className": "QBluetoothSocket", "enums": [{"isClass": true, "isFlag": false, "name": "SocketState", "values": ["UnconnectedState", "ServiceLookupState", "ConnectingState", "ConnectedState", "BoundState", "ClosingState", "ListeningState"]}, {"isClass": true, "isFlag": false, "name": "SocketError", "values": ["NoSocketError", "UnknownSocketError", "RemoteHostClosedError", "HostNotFoundError", "ServiceNotFoundError", "NetworkError", "UnsupportedProtocolError", "OperationError", "MissingPermissionsError"]}], "lineNumber": 21, "object": true, "qualifiedClassName": "QBluetoothSocket", "signals": [{"access": "public", "index": 0, "name": "connected", "returnType": "void"}, {"access": "public", "index": 1, "name": "disconnected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QBluetoothSocket::SocketError"}], "index": 2, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "QBluetoothSocket::SocketState"}], "index": 3, "name": "stateChanged", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "service", "type": "QBluetoothServiceInfo"}], "index": 4, "name": "serviceDiscovered", "returnType": "void"}, {"access": "private", "index": 5, "name": "discoveryFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QIODevice"}]}], "inputFile": "qbluetoothsocket.h", "outputRevision": 68}, {"classes": [{"className": "QBluetoothSocketBasePrivate", "lineNumber": 56, "object": true, "qualifiedClassName": "QBluetoothSocketBasePrivate", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qbluetoothsocketbase_p.h", "outputRevision": 68}, {"classes": [{"className": "QLowEnergyController", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "UnknownE<PERSON>r", "UnknownRemoteDeviceError", "NetworkError", "InvalidBluetoothAdapterError", "ConnectionError", "AdvertisingError", "RemoteHostClosedError", "AuthorizationError", "MissingPermissionsError", "RssiReadError"]}, {"isClass": false, "isFlag": false, "name": "ControllerState", "values": ["UnconnectedState", "ConnectingState", "ConnectedState", "DiscoveringState", "DiscoveredState", "ClosingState", "AdvertisingState"]}, {"isClass": false, "isFlag": false, "name": "RemoteAddressType", "values": ["PublicAddress", "Random<PERSON>dd<PERSON>"]}, {"isClass": false, "isFlag": false, "name": "Role", "values": ["CentralRole", "Peripher<PERSON><PERSON><PERSON>"]}], "lineNumber": 21, "object": true, "qualifiedClassName": "QLowEnergyController", "signals": [{"access": "public", "index": 0, "name": "connected", "returnType": "void"}, {"access": "public", "index": 1, "name": "disconnected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "QLowEnergyController::ControllerState"}], "index": 2, "name": "stateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "newError", "type": "QLowEnergyController::<PERSON><PERSON><PERSON>"}], "index": 3, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mtu", "type": "int"}], "index": 4, "name": "mtuChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rssi", "type": "qint16"}], "index": 5, "name": "rssiRead", "returnType": "void"}, {"access": "public", "arguments": [{"name": "newService", "type": "QBluetoothUuid"}], "index": 6, "name": "serviceDiscovered", "returnType": "void"}, {"access": "public", "index": 7, "name": "discoveryFinished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parameters", "type": "QLowEnergyConnectionParameters"}], "index": 8, "name": "connectionUpdated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qlowenergycontroller.h", "outputRevision": 68}, {"classes": [{"className": "QLowEnergyControllerPrivate", "lineNumber": 29, "object": true, "qualifiedClassName": "QLowEnergyControllerPrivate", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qlowenergycontrollerbase_p.h", "outputRevision": 68}, {"classes": [{"className": "QLowEnergyService", "enums": [{"isClass": false, "isFlag": false, "name": "ServiceType", "values": ["PrimaryService", "IncludedService"]}, {"isClass": false, "isFlag": false, "name": "ServiceError", "values": ["NoError", "OperationError", "CharacteristicWriteError", "DescriptorWriteError", "UnknownE<PERSON>r", "CharacteristicReadError", "DescriptorReadError"]}, {"isClass": false, "isFlag": false, "name": "ServiceState", "values": ["InvalidService", "RemoteService", "RemoteServiceDiscovering", "RemoteServiceDiscovered", "LocalService", "DiscoveryRequired", "DiscoveringService", "ServiceDiscovered"]}, {"isClass": false, "isFlag": false, "name": "DiscoveryMode", "values": ["FullDiscovery", "SkipValueDiscovery"]}, {"isClass": false, "isFlag": false, "name": "WriteMode", "values": ["WriteWithResponse", "WriteWithoutResponse", "WriteSigned"]}], "lineNumber": 14, "object": true, "qualifiedClassName": "QLowEnergyService", "signals": [{"access": "public", "arguments": [{"name": "newState", "type": "QLowEnergyService::ServiceState"}], "index": 0, "name": "stateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "info", "type": "QLowEnergyCharacteristic"}, {"name": "value", "type": "QByteArray"}], "index": 1, "name": "characteristicChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "info", "type": "QLowEnergyCharacteristic"}, {"name": "value", "type": "QByteArray"}], "index": 2, "name": "characteristicRead", "returnType": "void"}, {"access": "public", "arguments": [{"name": "info", "type": "QLowEnergyCharacteristic"}, {"name": "value", "type": "QByteArray"}], "index": 3, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "info", "type": "QLowEnergyDescriptor"}, {"name": "value", "type": "QByteArray"}], "index": 4, "name": "descriptorRead", "returnType": "void"}, {"access": "public", "arguments": [{"name": "info", "type": "QLowEnergyDescriptor"}, {"name": "value", "type": "QByteArray"}], "index": 5, "name": "descriptor<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QLowEnergyService::ServiceError"}], "index": 6, "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qlowenergyservice.h", "outputRevision": 68}, {"classes": [{"className": "QLowEnergyServicePrivate", "lineNumber": 33, "object": true, "qualifiedClassName": "QLowEnergyServicePrivate", "signals": [{"access": "public", "arguments": [{"name": "newState", "type": "QLowEnergyService::ServiceState"}], "index": 0, "name": "stateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QLowEnergyService::ServiceError"}], "index": 1, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "characteristic", "type": "QLowEnergyCharacteristic"}, {"name": "newValue", "type": "QByteArray"}], "index": 2, "name": "characteristicChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "info", "type": "QLowEnergyCharacteristic"}, {"name": "value", "type": "QByteArray"}], "index": 3, "name": "characteristicRead", "returnType": "void"}, {"access": "public", "arguments": [{"name": "characteristic", "type": "QLowEnergyCharacteristic"}, {"name": "newValue", "type": "QByteArray"}], "index": 4, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "info", "type": "QLowEnergyDescriptor"}, {"name": "value", "type": "QByteArray"}], "index": 5, "name": "descriptorRead", "returnType": "void"}, {"access": "public", "arguments": [{"name": "descriptor", "type": "QLowEnergyDescriptor"}, {"name": "newValue", "type": "QByteArray"}], "index": 6, "name": "descriptor<PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qlowenergyserviceprivate_p.h", "outputRevision": 68}]