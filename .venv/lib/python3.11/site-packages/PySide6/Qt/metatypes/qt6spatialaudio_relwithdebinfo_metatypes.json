[{"classes": [{"className": "QAmbientSound", "enums": [{"isClass": false, "isFlag": false, "name": "Loops", "values": ["Infinite", "Once"]}], "lineNumber": 16, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "source", "notify": "sourceChanged", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "volume", "notify": "volumeChanged", "read": "volume", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setVolume"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "loops", "notify": "loopsChanged", "read": "loops", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLoops"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "autoPlay", "notify": "autoPlayChanged", "read": "autoPlay", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoPlay"}], "qualifiedClassName": "QAmbientSound", "signals": [{"access": "public", "index": 0, "name": "sourceChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "loopsChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "autoPlayChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "volumeChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 4, "name": "play", "returnType": "void"}, {"access": "public", "index": 5, "name": "pause", "returnType": "void"}, {"access": "public", "index": 6, "name": "stop", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qambientsound.h", "outputRevision": 68}, {"classes": [{"className": "QAudioEngine", "enums": [{"isClass": false, "isFlag": false, "name": "OutputMode", "values": ["Surround", "Stereo", "Headphone"]}], "lineNumber": 15, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "outputMode", "notify": "outputModeChanged", "read": "outputMode", "required": false, "scriptable": true, "stored": true, "type": "OutputMode", "user": false, "write": "setOutputMode"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "outputDevice", "notify": "outputDeviceChanged", "read": "outputDevice", "required": false, "scriptable": true, "stored": true, "type": "QAudioDevice", "user": false, "write": "setOutputDevice"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "masterVolume", "notify": "masterVolumeChanged", "read": "masterVolume", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMasterVolume"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "paused", "notify": "paused<PERSON><PERSON>ed", "read": "paused", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPaused"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "distanceScale", "notify": "distanceScaleChanged", "read": "distanceScale", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setDistanceScale"}], "qualifiedClassName": "QAudioEngine", "signals": [{"access": "public", "index": 0, "name": "outputModeChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "outputDeviceChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "masterVolumeChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "paused<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "index": 4, "name": "distanceScaleChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 5, "name": "start", "returnType": "void"}, {"access": "public", "index": 6, "name": "stop", "returnType": "void"}, {"access": "public", "index": 7, "name": "pause", "returnType": "void"}, {"access": "public", "index": 8, "name": "resume", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qaudioengine.h", "outputRevision": 68}, {"classes": [{"className": "QAudioRoom", "lineNumber": 16, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setPosition"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "dimensions", "notify": "dimensionsChanged", "read": "dimensions", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setDimensions"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "rotation", "notify": "rotationChanged", "read": "rotation", "required": false, "scriptable": true, "stored": true, "type": "QQuaternion", "user": false, "write": "setRotation"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "reflectionGain", "notify": "reflection<PERSON>ain<PERSON><PERSON>ed", "read": "reflectionGain", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setReflectionGain"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "reverbGain", "notify": "reverbGain<PERSON><PERSON>ed", "read": "reverbGain", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setReverbGain"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "reverbTime", "notify": "reverbTimeChanged", "read": "reverbTime", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setReverbTime"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "reverbBrightness", "notify": "reverbBrightnessChanged", "read": "reverbBrightness", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setReverbBrightness"}], "qualifiedClassName": "QAudioRoom", "signals": [{"access": "public", "index": 0, "name": "positionChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "dimensionsChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "rotationChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "wallsChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "reflection<PERSON>ain<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "index": 5, "name": "reverbGain<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "index": 6, "name": "reverbTimeChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "reverbBrightnessChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qaudioroom.h", "outputRevision": 68}, {"classes": [{"className": "QSpatialSound", "enums": [{"isClass": false, "isFlag": false, "name": "Loops", "values": ["Infinite", "Once"]}, {"isClass": true, "isFlag": false, "name": "DistanceModel", "values": ["Logarithmic", "Linear", "ManualAttenuation"]}], "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "source", "notify": "sourceChanged", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setPosition"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "rotation", "notify": "rotationChanged", "read": "rotation", "required": false, "scriptable": true, "stored": true, "type": "QQuaternion", "user": false, "write": "setRotation"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "volume", "notify": "volumeChanged", "read": "volume", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setVolume"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "distanceModel", "notify": "distanceModelChanged", "read": "distanceModel", "required": false, "scriptable": true, "stored": true, "type": "DistanceModel", "user": false, "write": "setDistanceModel"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "size", "notify": "sizeChanged", "read": "size", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setSize"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "<PERSON><PERSON><PERSON><PERSON>", "notify": "distanceCutoff<PERSON>hanged", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "manualAttenuation", "notify": "manualAttenuationChanged", "read": "manualAttenuation", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setManualAttenuation"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "occlusionIntensity", "notify": "occlusionIntensityChanged", "read": "occlusionIntensity", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setOcclusionIntensity"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "directivity", "notify": "directivityChanged", "read": "directivity", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setDirectivity"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "directivityOrder", "notify": "directivityOrderChanged", "read": "directivityOrder", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setDirectivityOrder"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "nearFieldGain", "notify": "nearFieldGainChanged", "read": "nearFieldGain", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setNearFieldGain"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "loops", "notify": "loopsChanged", "read": "loops", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLoops"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "autoPlay", "notify": "autoPlayChanged", "read": "autoPlay", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoPlay"}], "qualifiedClassName": "QSpatialSound", "signals": [{"access": "public", "index": 0, "name": "sourceChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "loopsChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "autoPlayChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "positionChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "rotationChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "volumeChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "distanceModelChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "sizeChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "distanceCutoff<PERSON>hanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "manualAttenuationChanged", "returnType": "void"}, {"access": "public", "index": 10, "name": "occlusionIntensityChanged", "returnType": "void"}, {"access": "public", "index": 11, "name": "directivityChanged", "returnType": "void"}, {"access": "public", "index": 12, "name": "directivityOrderChanged", "returnType": "void"}, {"access": "public", "index": 13, "name": "nearFieldGainChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 14, "name": "play", "returnType": "void"}, {"access": "public", "index": 15, "name": "pause", "returnType": "void"}, {"access": "public", "index": 16, "name": "stop", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qspatialsound.h", "outputRevision": 68}, {"classes": [{"className": "QAudioOutputStream", "lineNumber": 29, "methods": [{"access": "public", "index": 0, "name": "startOutput", "returnType": "void"}, {"access": "public", "index": 1, "name": "stopOutput", "returnType": "void"}, {"access": "public", "index": 2, "name": "restartOutput", "returnType": "void"}], "object": true, "qualifiedClassName": "QAudioOutputStream", "superClasses": [{"access": "public", "name": "QIODevice"}]}], "inputFile": "qaudioengine.cpp", "outputRevision": 68}]