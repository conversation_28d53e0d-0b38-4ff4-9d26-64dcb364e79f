import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable -dependencies dependencies.json Qt3D.Animation 2.15'

Module {
    dependencies: ["Qt3D.Core 2.0"]
    Component {
        name: "Qt3DAnimation::QAbstractAnimation"
        prototype: "QObject"
        exports: ["Qt3D.Animation/AbstractAnimation 2.9"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "AnimationType"
            values: {
                "KeyframeAnimation": 1,
                "MorphingAnimation": 2,
                "VertexBlendAnimation": 3
            }
        }
        Property { name: "animationName"; type: "string" }
        Property { name: "animationType"; type: "AnimationType"; isReadonly: true }
        Property { name: "position"; type: "float" }
        Property { name: "duration"; type: "float"; isReadonly: true }
        Signal {
            name: "animationNameChanged"
            Parameter { name: "name"; type: "string" }
        }
        Signal {
            name: "positionChanged"
            Parameter { name: "position"; type: "float" }
        }
        Signal {
            name: "durationChanged"
            Parameter { name: "duration"; type: "float" }
        }
        Method {
            name: "setAnimationName"
            Parameter { name: "name"; type: "string" }
        }
        Method {
            name: "setPosition"
            Parameter { name: "position"; type: "float" }
        }
    }
    Component {
        name: "Qt3DAnimation::QAbstractAnimationClip"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Animation/AbstractAnimationClip 2.9"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "duration"; type: "float"; isReadonly: true }
        Signal {
            name: "durationChanged"
            Parameter { name: "duration"; type: "float" }
        }
    }
    Component {
        name: "Qt3DAnimation::QAbstractChannelMapping"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Animation/AbstractChannelMapping 2.10"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "Qt3DAnimation::QAbstractClipAnimator"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Animation/AbstractClipAnimator 2.9"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Loops"
            values: {
                "Infinite": -1
            }
        }
        Property { name: "running"; type: "bool" }
        Property { name: "loops"; type: "int" }
        Property { name: "channelMapper"; type: "Qt3DAnimation::QChannelMapper"; isPointer: true }
        Property { name: "clock"; type: "Qt3DAnimation::QClock"; isPointer: true }
        Property { name: "normalizedTime"; type: "float" }
        Signal {
            name: "runningChanged"
            Parameter { name: "running"; type: "bool" }
        }
        Signal {
            name: "channelMapperChanged"
            Parameter { name: "channelMapper"; type: "Qt3DAnimation::QChannelMapper"; isPointer: true }
        }
        Signal {
            name: "loopCountChanged"
            Parameter { name: "loops"; type: "int" }
        }
        Signal {
            name: "clockChanged"
            Parameter { name: "clock"; type: "Qt3DAnimation::QClock"; isPointer: true }
        }
        Signal {
            name: "normalizedTimeChanged"
            Parameter { name: "index"; type: "float" }
        }
        Method {
            name: "setRunning"
            Parameter { name: "running"; type: "bool" }
        }
        Method {
            name: "setChannelMapper"
            Parameter { name: "channelMapper"; type: "Qt3DAnimation::QChannelMapper"; isPointer: true }
        }
        Method {
            name: "setLoopCount"
            Parameter { name: "loops"; type: "int" }
        }
        Method {
            name: "setClock"
            Parameter { name: "clock"; type: "Qt3DAnimation::QClock"; isPointer: true }
        }
        Method {
            name: "setNormalizedTime"
            Parameter { name: "timeFraction"; type: "float" }
        }
        Method { name: "start" }
        Method { name: "stop" }
    }
    Component {
        name: "Qt3DAnimation::QAbstractClipBlendNode"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Animation/AbstractClipBlendNode 2.9"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "Qt3DAnimation::QAdditiveClipBlend"
        prototype: "Qt3DAnimation::QAbstractClipBlendNode"
        exports: ["Qt3D.Animation/AdditiveClipBlend 2.9"]
        exportMetaObjectRevisions: [0]
        Property { name: "baseClip"; type: "Qt3DAnimation::QAbstractClipBlendNode"; isPointer: true }
        Property {
            name: "additiveClip"
            type: "Qt3DAnimation::QAbstractClipBlendNode"
            isPointer: true
        }
        Property { name: "additiveFactor"; type: "float" }
        Signal {
            name: "additiveFactorChanged"
            Parameter { name: "additiveFactor"; type: "float" }
        }
        Signal {
            name: "baseClipChanged"
            Parameter { name: "baseClip"; type: "Qt3DAnimation::QAbstractClipBlendNode"; isPointer: true }
        }
        Signal {
            name: "additiveClipChanged"
            Parameter {
                name: "additiveClip"
                type: "Qt3DAnimation::QAbstractClipBlendNode"
                isPointer: true
            }
        }
        Method {
            name: "setAdditiveFactor"
            Parameter { name: "additiveFactor"; type: "float" }
        }
        Method {
            name: "setBaseClip"
            Parameter { name: "baseClip"; type: "Qt3DAnimation::QAbstractClipBlendNode"; isPointer: true }
        }
        Method {
            name: "setAdditiveClip"
            Parameter {
                name: "additiveClip"
                type: "Qt3DAnimation::QAbstractClipBlendNode"
                isPointer: true
            }
        }
    }
    Component {
        name: "Qt3DAnimation::QAnimationClip"
        prototype: "Qt3DAnimation::QAbstractAnimationClip"
        exports: ["Qt3D.Animation/AnimationClip 2.9"]
        exportMetaObjectRevisions: [0]
        Property { name: "clipData"; type: "Qt3DAnimation::QAnimationClipData" }
        Signal {
            name: "clipDataChanged"
            Parameter { name: "clipData"; type: "Qt3DAnimation::QAnimationClipData" }
        }
        Method {
            name: "setClipData"
            Parameter { name: "clipData"; type: "Qt3DAnimation::QAnimationClipData" }
        }
    }
    Component {
        name: "Qt3DAnimation::QAnimationClipLoader"
        prototype: "Qt3DAnimation::QAbstractAnimationClip"
        exports: ["Qt3D.Animation/AnimationClipLoader 2.9"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Status"
            values: {
                "NotReady": 0,
                "Ready": 1,
                "Error": 2
            }
        }
        Property { name: "source"; type: "QUrl" }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Signal {
            name: "sourceChanged"
            Parameter { name: "source"; type: "QUrl" }
        }
        Signal {
            name: "statusChanged"
            Parameter { name: "status"; type: "Status" }
        }
        Method {
            name: "setSource"
            Parameter { name: "source"; type: "QUrl" }
        }
    }
    Component {
        name: "Qt3DAnimation::QAnimationController"
        prototype: "QObject"
        exports: ["Qt3D.Animation/AnimationController 2.9"]
        exportMetaObjectRevisions: [209]
        Property { name: "activeAnimationGroup"; type: "int" }
        Property { name: "position"; type: "float" }
        Property { name: "positionScale"; type: "float" }
        Property { name: "positionOffset"; type: "float" }
        Property { name: "entity"; type: "Qt3DCore::QEntity"; isPointer: true }
        Property { name: "recursive"; type: "bool" }
        Signal {
            name: "activeAnimationGroupChanged"
            Parameter { name: "index"; type: "int" }
        }
        Signal {
            name: "positionChanged"
            Parameter { name: "position"; type: "float" }
        }
        Signal {
            name: "positionScaleChanged"
            Parameter { name: "scale"; type: "float" }
        }
        Signal {
            name: "positionOffsetChanged"
            Parameter { name: "offset"; type: "float" }
        }
        Signal {
            name: "entityChanged"
            Parameter { name: "entity"; type: "Qt3DCore::QEntity"; isPointer: true }
        }
        Signal {
            name: "recursiveChanged"
            Parameter { name: "recursive"; type: "bool" }
        }
        Method {
            name: "setActiveAnimationGroup"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "setPosition"
            Parameter { name: "position"; type: "float" }
        }
        Method {
            name: "setPositionScale"
            Parameter { name: "scale"; type: "float" }
        }
        Method {
            name: "setPositionOffset"
            Parameter { name: "offset"; type: "float" }
        }
        Method {
            name: "setEntity"
            Parameter { name: "entity"; type: "Qt3DCore::QEntity"; isPointer: true }
        }
        Method {
            name: "setRecursive"
            Parameter { name: "recursive"; type: "bool" }
        }
        Method {
            name: "getAnimationIndex"
            type: "int"
            Parameter { name: "name"; type: "string" }
        }
        Method {
            name: "getGroup"
            type: "Qt3DAnimation::QAnimationGroup*"
            Parameter { name: "index"; type: "int" }
        }
        Property {
            name: "animationGroups"
            revision: 209
            type: "Qt3DAnimation::QAnimationGroup"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DAnimation::QAnimationGroup"
        prototype: "QObject"
        exports: ["Qt3D.Animation/AnimationGroup 2.9"]
        exportMetaObjectRevisions: [209]
        Property { name: "name"; type: "string" }
        Property { name: "position"; type: "float" }
        Property { name: "duration"; type: "float"; isReadonly: true }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "string" }
        }
        Signal {
            name: "positionChanged"
            Parameter { name: "position"; type: "float" }
        }
        Signal {
            name: "durationChanged"
            Parameter { name: "duration"; type: "float" }
        }
        Method {
            name: "setName"
            Parameter { name: "name"; type: "string" }
        }
        Method {
            name: "setPosition"
            Parameter { name: "position"; type: "float" }
        }
        Property {
            name: "animations"
            revision: 209
            type: "Qt3DAnimation::QAbstractAnimation"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DAnimation::QBlendedClipAnimator"
        prototype: "Qt3DAnimation::QAbstractClipAnimator"
        exports: ["Qt3D.Animation/BlendedClipAnimator 2.9"]
        exportMetaObjectRevisions: [0]
        Property { name: "blendTree"; type: "Qt3DAnimation::QAbstractClipBlendNode"; isPointer: true }
        Signal {
            name: "blendTreeChanged"
            Parameter { name: "blendTree"; type: "QAbstractClipBlendNode"; isPointer: true }
        }
        Method {
            name: "setBlendTree"
            Parameter { name: "blendTree"; type: "QAbstractClipBlendNode"; isPointer: true }
        }
    }
    Component {
        name: "Qt3DAnimation::QChannelMapper"
        defaultProperty: "mappings"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Animation/ChannelMapper 2.9"]
        exportMetaObjectRevisions: [209]
        Property {
            name: "mappings"
            revision: 209
            type: "Qt3DAnimation::QAbstractChannelMapping"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DAnimation::QChannelMapping"
        prototype: "Qt3DAnimation::QAbstractChannelMapping"
        exports: ["Qt3D.Animation/ChannelMapping 2.9"]
        exportMetaObjectRevisions: [0]
        Property { name: "channelName"; type: "string" }
        Property { name: "target"; type: "Qt3DCore::QNode"; isPointer: true }
        Property { name: "property"; type: "string" }
        Signal {
            name: "channelNameChanged"
            Parameter { name: "channelName"; type: "string" }
        }
        Signal {
            name: "targetChanged"
            Parameter { name: "target"; type: "Qt3DCore::QNode"; isPointer: true }
        }
        Signal {
            name: "propertyChanged"
            Parameter { name: "property"; type: "string" }
        }
        Method {
            name: "setChannelName"
            Parameter { name: "channelName"; type: "string" }
        }
        Method {
            name: "setTarget"
            Parameter { name: "target"; type: "Qt3DCore::QNode"; isPointer: true }
        }
        Method {
            name: "setProperty"
            Parameter { name: "property"; type: "string" }
        }
    }
    Component {
        name: "Qt3DAnimation::QClipAnimator"
        prototype: "Qt3DAnimation::QAbstractClipAnimator"
        exports: ["Qt3D.Animation/ClipAnimator 2.9"]
        exportMetaObjectRevisions: [0]
        Property { name: "clip"; type: "Qt3DAnimation::QAbstractAnimationClip"; isPointer: true }
        Signal {
            name: "clipChanged"
            Parameter { name: "clip"; type: "Qt3DAnimation::QAbstractAnimationClip"; isPointer: true }
        }
        Method {
            name: "setClip"
            Parameter { name: "clip"; type: "Qt3DAnimation::QAbstractAnimationClip"; isPointer: true }
        }
    }
    Component {
        name: "Qt3DAnimation::QClipBlendValue"
        prototype: "Qt3DAnimation::QAbstractClipBlendNode"
        exports: ["Qt3D.Animation/ClipBlendValue 2.9"]
        exportMetaObjectRevisions: [0]
        Property { name: "clip"; type: "Qt3DAnimation::QAbstractAnimationClip"; isPointer: true }
        Signal {
            name: "clipChanged"
            Parameter { name: "clip"; type: "Qt3DAnimation::QAbstractAnimationClip"; isPointer: true }
        }
        Method {
            name: "setClip"
            Parameter { name: "clip"; type: "Qt3DAnimation::QAbstractAnimationClip"; isPointer: true }
        }
    }
    Component {
        name: "Qt3DAnimation::QClock"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Animation/Clock 2.9"]
        exportMetaObjectRevisions: [0]
        Property { name: "playbackRate"; type: "double" }
        Signal {
            name: "playbackRateChanged"
            Parameter { name: "playbackRate"; type: "double" }
        }
    }
    Component {
        name: "Qt3DAnimation::QKeyframeAnimation"
        prototype: "Qt3DAnimation::QAbstractAnimation"
        exports: ["Qt3D.Animation/KeyframeAnimation 2.9"]
        exportMetaObjectRevisions: [209]
        Enum {
            name: "RepeatMode"
            values: {
                "None": 0,
                "Constant": 1,
                "Repeat": 2
            }
        }
        Property { name: "framePositions"; type: "QVector<float>" }
        Property { name: "target"; type: "Qt3DCore::QTransform"; isPointer: true }
        Property { name: "easing"; type: "QEasingCurve" }
        Property { name: "targetName"; type: "string" }
        Property { name: "startMode"; type: "RepeatMode" }
        Property { name: "endMode"; type: "RepeatMode" }
        Signal {
            name: "framePositionsChanged"
            Parameter { name: "positions"; type: "QVector<float>" }
        }
        Signal {
            name: "targetChanged"
            Parameter { name: "target"; type: "Qt3DCore::QTransform"; isPointer: true }
        }
        Signal {
            name: "easingChanged"
            Parameter { name: "easing"; type: "QEasingCurve" }
        }
        Signal {
            name: "targetNameChanged"
            Parameter { name: "name"; type: "string" }
        }
        Signal {
            name: "startModeChanged"
            Parameter { name: "startMode"; type: "QKeyframeAnimation::RepeatMode" }
        }
        Signal {
            name: "endModeChanged"
            Parameter { name: "endMode"; type: "QKeyframeAnimation::RepeatMode" }
        }
        Method {
            name: "setFramePositions"
            Parameter { name: "positions"; type: "QVector<float>" }
        }
        Method {
            name: "setTarget"
            Parameter { name: "target"; type: "Qt3DCore::QTransform"; isPointer: true }
        }
        Method {
            name: "setEasing"
            Parameter { name: "easing"; type: "QEasingCurve" }
        }
        Method {
            name: "setTargetName"
            Parameter { name: "name"; type: "string" }
        }
        Method {
            name: "setStartMode"
            Parameter { name: "mode"; type: "RepeatMode" }
        }
        Method {
            name: "setEndMode"
            Parameter { name: "mode"; type: "RepeatMode" }
        }
        Property {
            name: "keyframes"
            revision: 209
            type: "Qt3DCore::QTransform"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DAnimation::QLerpClipBlend"
        prototype: "Qt3DAnimation::QAbstractClipBlendNode"
        exports: ["Qt3D.Animation/LerpClipBlend 2.9"]
        exportMetaObjectRevisions: [0]
        Property { name: "startClip"; type: "Qt3DAnimation::QAbstractClipBlendNode"; isPointer: true }
        Property { name: "endClip"; type: "Qt3DAnimation::QAbstractClipBlendNode"; isPointer: true }
        Property { name: "blendFactor"; type: "float" }
        Signal {
            name: "blendFactorChanged"
            Parameter { name: "blendFactor"; type: "float" }
        }
        Signal {
            name: "startClipChanged"
            Parameter { name: "startClip"; type: "Qt3DAnimation::QAbstractClipBlendNode"; isPointer: true }
        }
        Signal {
            name: "endClipChanged"
            Parameter { name: "endClip"; type: "Qt3DAnimation::QAbstractClipBlendNode"; isPointer: true }
        }
        Method {
            name: "setBlendFactor"
            Parameter { name: "blendFactor"; type: "float" }
        }
        Method {
            name: "setStartClip"
            Parameter { name: "startClip"; type: "Qt3DAnimation::QAbstractClipBlendNode"; isPointer: true }
        }
        Method {
            name: "setEndClip"
            Parameter { name: "endClip"; type: "Qt3DAnimation::QAbstractClipBlendNode"; isPointer: true }
        }
    }
    Component {
        name: "Qt3DAnimation::QMorphTarget"
        prototype: "QObject"
        exports: ["Qt3D.Animation/MorphTarget 2.9"]
        exportMetaObjectRevisions: [209]
        Property { name: "attributeNames"; type: "QStringList"; isReadonly: true }
        Signal {
            name: "attributeNamesChanged"
            Parameter { name: "attributeNames"; type: "QStringList" }
        }
        Method {
            name: "fromGeometry"
            type: "QMorphTarget*"
            Parameter { name: "geometry"; type: "Qt3DRender::QGeometry"; isPointer: true }
            Parameter { name: "attributes"; type: "QStringList" }
        }
        Property {
            name: "attributes"
            revision: 209
            type: "Qt3DRender::QAttribute"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DAnimation::QMorphingAnimation"
        prototype: "Qt3DAnimation::QAbstractAnimation"
        exports: ["Qt3D.Animation/MorphingAnimation 2.9"]
        exportMetaObjectRevisions: [209]
        Enum {
            name: "Method"
            values: {
                "Normalized": 0,
                "Relative": 1
            }
        }
        Property { name: "targetPositions"; type: "QVector<float>" }
        Property { name: "interpolator"; type: "float"; isReadonly: true }
        Property { name: "target"; type: "Qt3DRender::QGeometryRenderer"; isPointer: true }
        Property { name: "targetName"; type: "string" }
        Property { name: "method"; type: "Method" }
        Property { name: "easing"; type: "QEasingCurve" }
        Signal {
            name: "targetPositionsChanged"
            Parameter { name: "targetPositions"; type: "QVector<float>" }
        }
        Signal {
            name: "interpolatorChanged"
            Parameter { name: "interpolator"; type: "float" }
        }
        Signal {
            name: "targetChanged"
            Parameter { name: "target"; type: "Qt3DRender::QGeometryRenderer"; isPointer: true }
        }
        Signal {
            name: "targetNameChanged"
            Parameter { name: "name"; type: "string" }
        }
        Signal {
            name: "methodChanged"
            Parameter { name: "method"; type: "QMorphingAnimation::Method" }
        }
        Signal {
            name: "easingChanged"
            Parameter { name: "easing"; type: "QEasingCurve" }
        }
        Method {
            name: "setTargetPositions"
            Parameter { name: "targetPositions"; type: "QVector<float>" }
        }
        Method {
            name: "setTarget"
            Parameter { name: "target"; type: "Qt3DRender::QGeometryRenderer"; isPointer: true }
        }
        Method {
            name: "setTargetName"
            Parameter { name: "name"; type: "string" }
        }
        Method {
            name: "setMethod"
            Parameter { name: "method"; type: "QMorphingAnimation::Method" }
        }
        Method {
            name: "setEasing"
            Parameter { name: "easing"; type: "QEasingCurve" }
        }
        Property {
            name: "morphTargets"
            revision: 209
            type: "Qt3DAnimation::QMorphTarget"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DAnimation::QSkeletonMapping"
        prototype: "Qt3DAnimation::QAbstractChannelMapping"
        exports: ["Qt3D.Animation/SkeletonMapping 2.10"]
        exportMetaObjectRevisions: [0]
        Property { name: "skeleton"; type: "Qt3DCore::QAbstractSkeleton"; isPointer: true }
        Signal {
            name: "skeletonChanged"
            Parameter { name: "skeleton"; type: "Qt3DCore::QAbstractSkeleton"; isPointer: true }
        }
        Method {
            name: "setSkeleton"
            Parameter { name: "skeleton"; type: "Qt3DCore::QAbstractSkeleton"; isPointer: true }
        }
    }
    Component {
        name: "Qt3DAnimation::QVertexBlendAnimation"
        prototype: "Qt3DAnimation::QAbstractAnimation"
        exports: ["Qt3D.Animation/VertexBlendAnimation 2.9"]
        exportMetaObjectRevisions: [209]
        Property { name: "targetPositions"; type: "QVector<float>" }
        Property { name: "interpolator"; type: "float"; isReadonly: true }
        Property { name: "target"; type: "Qt3DRender::QGeometryRenderer"; isPointer: true }
        Property { name: "targetName"; type: "string" }
        Signal {
            name: "targetPositionsChanged"
            Parameter { name: "targetPositions"; type: "QVector<float>" }
        }
        Signal {
            name: "interpolatorChanged"
            Parameter { name: "interpolator"; type: "float" }
        }
        Signal {
            name: "targetChanged"
            Parameter { name: "target"; type: "Qt3DRender::QGeometryRenderer"; isPointer: true }
        }
        Signal {
            name: "targetNameChanged"
            Parameter { name: "name"; type: "string" }
        }
        Method {
            name: "setTargetPositions"
            Parameter { name: "targetPositions"; type: "QVector<float>" }
        }
        Method {
            name: "setTarget"
            Parameter { name: "target"; type: "Qt3DRender::QGeometryRenderer"; isPointer: true }
        }
        Method {
            name: "setTargetName"
            Parameter { name: "name"; type: "string" }
        }
        Property {
            name: "morphTargets"
            revision: 209
            type: "Qt3DAnimation::QMorphTarget"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DCore::QNode"
        defaultProperty: "data"
        prototype: "QObject"
        exports: ["Qt3D.Animation/Node 2.9"]
        isCreatable: false
        exportMetaObjectRevisions: [209]
        Enum {
            name: "PropertyTrackingMode"
            values: {
                "TrackFinalValues": 0,
                "DontTrackValues": 1,
                "TrackAllValues": 2
            }
        }
        Property { name: "parent"; type: "Qt3DCore::QNode"; isPointer: true }
        Property { name: "enabled"; type: "bool" }
        Property { name: "defaultPropertyTrackingMode"; revision: 9; type: "PropertyTrackingMode" }
        Signal {
            name: "parentChanged"
            Parameter { name: "parent"; type: "QObject"; isPointer: true }
        }
        Signal {
            name: "enabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "defaultPropertyTrackingModeChanged"
            Parameter { name: "mode"; type: "PropertyTrackingMode" }
        }
        Signal { name: "nodeDestroyed" }
        Method {
            name: "setParent"
            Parameter { name: "parent"; type: "QNode"; isPointer: true }
        }
        Method {
            name: "setEnabled"
            Parameter { name: "isEnabled"; type: "bool" }
        }
        Method {
            name: "setDefaultPropertyTrackingMode"
            Parameter { name: "mode"; type: "PropertyTrackingMode" }
        }
        Property { name: "propertyTrackingOverrides"; revision: 209; type: "QJSValue" }
        Property { name: "data"; revision: 209; type: "QObject"; isList: true; isReadonly: true }
        Property {
            name: "childNodes"
            revision: 209
            type: "Qt3DCore::QNode"
            isList: true
            isReadonly: true
        }
    }
}
