import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable -dependencies dependencies.json Qt3D.Extras 2.15'

Module {
    dependencies: ["Qt3D.Logic 2.0", "Qt3D.Render 2.0"]
    Component { name: "Qt3DCore::QEntity"; prototype: "Qt3DCore::QNode" }
    Component {
        name: "Qt3DCore::QNode"
        prototype: "QObject"
        Enum {
            name: "PropertyTrackingMode"
            values: {
                "TrackFinalValues": 0,
                "DontTrackValues": 1,
                "TrackAllValues": 2
            }
        }
        Property { name: "parent"; type: "Qt3DCore::QNode"; isPointer: true }
        Property { name: "enabled"; type: "bool" }
        Property { name: "defaultPropertyTrackingMode"; revision: 9; type: "PropertyTrackingMode" }
        Signal {
            name: "parentChanged"
            Parameter { name: "parent"; type: "QObject"; isPointer: true }
        }
        Signal {
            name: "enabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "defaultPropertyTrackingModeChanged"
            Parameter { name: "mode"; type: "PropertyTrackingMode" }
        }
        Signal { name: "nodeDestroyed" }
        Method {
            name: "setParent"
            Parameter { name: "parent"; type: "QNode"; isPointer: true }
        }
        Method {
            name: "setEnabled"
            Parameter { name: "isEnabled"; type: "bool" }
        }
        Method {
            name: "setDefaultPropertyTrackingMode"
            Parameter { name: "mode"; type: "PropertyTrackingMode" }
        }
    }
    Component {
        name: "Qt3DExtras::Extras::Quick::Quick3DLevelOfDetailLoader"
        prototype: "Qt3DCore::QEntity"
        exports: ["Qt3D.Extras/LevelOfDetailLoader 2.9"]
        exportMetaObjectRevisions: [0]
        Property { name: "sources"; type: "QVariantList" }
        Property { name: "camera"; type: "Qt3DRender::QCamera"; isPointer: true }
        Property { name: "currentIndex"; type: "int" }
        Property { name: "thresholdType"; type: "Qt3DRender::QLevelOfDetail::ThresholdType" }
        Property { name: "thresholds"; type: "QVector<qreal>" }
        Property { name: "volumeOverride"; type: "Qt3DRender::QLevelOfDetailBoundingSphere" }
        Property { name: "entity"; type: "QObject"; isReadonly: true; isPointer: true }
        Property { name: "source"; type: "QUrl"; isReadonly: true }
        Method {
            name: "createBoundingSphere"
            type: "Qt3DRender::QLevelOfDetailBoundingSphere"
            Parameter { name: "center"; type: "QVector3D" }
            Parameter { name: "radius"; type: "float" }
        }
    }
    Component {
        name: "Qt3DExtras::QAbstractCameraController"
        prototype: "Qt3DCore::QEntity"
        Property { name: "camera"; type: "Qt3DRender::QCamera"; isPointer: true }
        Property { name: "linearSpeed"; type: "float" }
        Property { name: "lookSpeed"; type: "float" }
        Property { name: "acceleration"; type: "float" }
        Property { name: "deceleration"; type: "float" }
        Signal {
            name: "accelerationChanged"
            Parameter { name: "acceleration"; type: "float" }
        }
        Signal {
            name: "decelerationChanged"
            Parameter { name: "deceleration"; type: "float" }
        }
    }
    Component {
        name: "Qt3DExtras::QAbstractSpriteSheet"
        prototype: "Qt3DCore::QNode"
        Property { name: "texture"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        Property { name: "textureTransform"; type: "QMatrix3x3"; isReadonly: true }
        Property { name: "currentIndex"; type: "int" }
        Signal {
            name: "textureChanged"
            Parameter { name: "texture"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "textureTransformChanged"
            Parameter { name: "textureTransform"; type: "QMatrix3x3" }
        }
        Signal {
            name: "currentIndexChanged"
            Parameter { name: "currentIndex"; type: "int" }
        }
        Method {
            name: "setTexture"
            Parameter { name: "texture"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setCurrentIndex"
            Parameter { name: "currentIndex"; type: "int" }
        }
    }
    Component {
        name: "Qt3DExtras::QConeGeometry"
        prototype: "Qt3DRender::QGeometry"
        exports: ["Qt3D.Extras/ConeGeometry 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "hasTopEndcap"; type: "bool" }
        Property { name: "hasBottomEndcap"; type: "bool" }
        Property { name: "rings"; type: "int" }
        Property { name: "slices"; type: "int" }
        Property { name: "topRadius"; type: "float" }
        Property { name: "bottomRadius"; type: "float" }
        Property { name: "length"; type: "float" }
        Property {
            name: "positionAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "normalAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "texCoordAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "indexAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Signal {
            name: "hasTopEndcapChanged"
            Parameter { name: "hasTopEndcap"; type: "bool" }
        }
        Signal {
            name: "hasBottomEndcapChanged"
            Parameter { name: "hasBottomEndcap"; type: "bool" }
        }
        Signal {
            name: "topRadiusChanged"
            Parameter { name: "topRadius"; type: "float" }
        }
        Signal {
            name: "bottomRadiusChanged"
            Parameter { name: "bottomRadius"; type: "float" }
        }
        Signal {
            name: "ringsChanged"
            Parameter { name: "rings"; type: "int" }
        }
        Signal {
            name: "slicesChanged"
            Parameter { name: "slices"; type: "int" }
        }
        Signal {
            name: "lengthChanged"
            Parameter { name: "length"; type: "float" }
        }
        Method {
            name: "setHasTopEndcap"
            Parameter { name: "hasTopEndcap"; type: "bool" }
        }
        Method {
            name: "setHasBottomEndcap"
            Parameter { name: "hasBottomEndcap"; type: "bool" }
        }
        Method {
            name: "setTopRadius"
            Parameter { name: "topRadius"; type: "float" }
        }
        Method {
            name: "setBottomRadius"
            Parameter { name: "bottomRadius"; type: "float" }
        }
        Method {
            name: "setRings"
            Parameter { name: "rings"; type: "int" }
        }
        Method {
            name: "setSlices"
            Parameter { name: "slices"; type: "int" }
        }
        Method {
            name: "setLength"
            Parameter { name: "length"; type: "float" }
        }
    }
    Component {
        name: "Qt3DExtras::QConeMesh"
        prototype: "Qt3DRender::QGeometryRenderer"
        exports: ["Qt3D.Extras/ConeMesh 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "rings"; type: "int" }
        Property { name: "slices"; type: "int" }
        Property { name: "hasTopEndcap"; type: "bool" }
        Property { name: "hasBottomEndcap"; type: "bool" }
        Property { name: "topRadius"; type: "float" }
        Property { name: "bottomRadius"; type: "float" }
        Property { name: "length"; type: "float" }
        Signal {
            name: "hasTopEndcapChanged"
            Parameter { name: "hasTopEndcap"; type: "bool" }
        }
        Signal {
            name: "hasBottomEndcapChanged"
            Parameter { name: "hasBottomEndcap"; type: "bool" }
        }
        Signal {
            name: "topRadiusChanged"
            Parameter { name: "topRadius"; type: "float" }
        }
        Signal {
            name: "bottomRadiusChanged"
            Parameter { name: "bottomRadius"; type: "float" }
        }
        Signal {
            name: "ringsChanged"
            Parameter { name: "rings"; type: "int" }
        }
        Signal {
            name: "slicesChanged"
            Parameter { name: "slices"; type: "int" }
        }
        Signal {
            name: "lengthChanged"
            Parameter { name: "length"; type: "float" }
        }
        Method {
            name: "setHasTopEndcap"
            Parameter { name: "hasTopEndcap"; type: "bool" }
        }
        Method {
            name: "setHasBottomEndcap"
            Parameter { name: "hasBottomEndcap"; type: "bool" }
        }
        Method {
            name: "setTopRadius"
            Parameter { name: "topRadius"; type: "float" }
        }
        Method {
            name: "setBottomRadius"
            Parameter { name: "bottomRadius"; type: "float" }
        }
        Method {
            name: "setRings"
            Parameter { name: "rings"; type: "int" }
        }
        Method {
            name: "setSlices"
            Parameter { name: "slices"; type: "int" }
        }
        Method {
            name: "setLength"
            Parameter { name: "length"; type: "float" }
        }
    }
    Component {
        name: "Qt3DExtras::QCuboidGeometry"
        prototype: "Qt3DRender::QGeometry"
        exports: ["Qt3D.Extras/CuboidGeometry 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "xExtent"; type: "float" }
        Property { name: "yExtent"; type: "float" }
        Property { name: "zExtent"; type: "float" }
        Property { name: "xyMeshResolution"; type: "QSize" }
        Property { name: "yzMeshResolution"; type: "QSize" }
        Property { name: "xzMeshResolution"; type: "QSize" }
        Property {
            name: "positionAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "normalAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "texCoordAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "tangentAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "indexAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Signal {
            name: "xExtentChanged"
            Parameter { name: "xExtent"; type: "float" }
        }
        Signal {
            name: "yExtentChanged"
            Parameter { name: "yExtent"; type: "float" }
        }
        Signal {
            name: "zExtentChanged"
            Parameter { name: "zExtent"; type: "float" }
        }
        Signal {
            name: "yzMeshResolutionChanged"
            Parameter { name: "yzMeshResolution"; type: "QSize" }
        }
        Signal {
            name: "xzMeshResolutionChanged"
            Parameter { name: "xzMeshResolution"; type: "QSize" }
        }
        Signal {
            name: "xyMeshResolutionChanged"
            Parameter { name: "xyMeshResolution"; type: "QSize" }
        }
        Method {
            name: "setXExtent"
            Parameter { name: "xExtent"; type: "float" }
        }
        Method {
            name: "setYExtent"
            Parameter { name: "yExtent"; type: "float" }
        }
        Method {
            name: "setZExtent"
            Parameter { name: "zExtent"; type: "float" }
        }
        Method {
            name: "setYZMeshResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
        Method {
            name: "setXZMeshResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
        Method {
            name: "setXYMeshResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
    }
    Component {
        name: "Qt3DExtras::QCuboidMesh"
        prototype: "Qt3DRender::QGeometryRenderer"
        exports: ["Qt3D.Extras/CuboidMesh 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "xExtent"; type: "float" }
        Property { name: "yExtent"; type: "float" }
        Property { name: "zExtent"; type: "float" }
        Property { name: "yzMeshResolution"; type: "QSize" }
        Property { name: "xzMeshResolution"; type: "QSize" }
        Property { name: "xyMeshResolution"; type: "QSize" }
        Signal {
            name: "xExtentChanged"
            Parameter { name: "xExtent"; type: "float" }
        }
        Signal {
            name: "yExtentChanged"
            Parameter { name: "yExtent"; type: "float" }
        }
        Signal {
            name: "zExtentChanged"
            Parameter { name: "zExtent"; type: "float" }
        }
        Signal {
            name: "yzMeshResolutionChanged"
            Parameter { name: "yzMeshResolution"; type: "QSize" }
        }
        Signal {
            name: "xzMeshResolutionChanged"
            Parameter { name: "xzMeshResolution"; type: "QSize" }
        }
        Signal {
            name: "xyMeshResolutionChanged"
            Parameter { name: "xyMeshResolution"; type: "QSize" }
        }
        Method {
            name: "setXExtent"
            Parameter { name: "xExtent"; type: "float" }
        }
        Method {
            name: "setYExtent"
            Parameter { name: "yExtent"; type: "float" }
        }
        Method {
            name: "setZExtent"
            Parameter { name: "zExtent"; type: "float" }
        }
        Method {
            name: "setYZMeshResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
        Method {
            name: "setXZMeshResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
        Method {
            name: "setXYMeshResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
    }
    Component {
        name: "Qt3DExtras::QCylinderGeometry"
        prototype: "Qt3DRender::QGeometry"
        exports: ["Qt3D.Extras/CylinderGeometry 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "rings"; type: "int" }
        Property { name: "slices"; type: "int" }
        Property { name: "radius"; type: "float" }
        Property { name: "length"; type: "float" }
        Property {
            name: "positionAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "normalAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "texCoordAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "indexAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Signal {
            name: "radiusChanged"
            Parameter { name: "radius"; type: "float" }
        }
        Signal {
            name: "ringsChanged"
            Parameter { name: "rings"; type: "int" }
        }
        Signal {
            name: "slicesChanged"
            Parameter { name: "slices"; type: "int" }
        }
        Signal {
            name: "lengthChanged"
            Parameter { name: "length"; type: "float" }
        }
        Method {
            name: "setRings"
            Parameter { name: "rings"; type: "int" }
        }
        Method {
            name: "setSlices"
            Parameter { name: "slices"; type: "int" }
        }
        Method {
            name: "setRadius"
            Parameter { name: "radius"; type: "float" }
        }
        Method {
            name: "setLength"
            Parameter { name: "length"; type: "float" }
        }
    }
    Component {
        name: "Qt3DExtras::QCylinderMesh"
        prototype: "Qt3DRender::QGeometryRenderer"
        exports: ["Qt3D.Extras/CylinderMesh 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "rings"; type: "int" }
        Property { name: "slices"; type: "int" }
        Property { name: "radius"; type: "float" }
        Property { name: "length"; type: "float" }
        Signal {
            name: "radiusChanged"
            Parameter { name: "radius"; type: "float" }
        }
        Signal {
            name: "ringsChanged"
            Parameter { name: "rings"; type: "int" }
        }
        Signal {
            name: "slicesChanged"
            Parameter { name: "slices"; type: "int" }
        }
        Signal {
            name: "lengthChanged"
            Parameter { name: "length"; type: "float" }
        }
        Method {
            name: "setRings"
            Parameter { name: "rings"; type: "int" }
        }
        Method {
            name: "setSlices"
            Parameter { name: "slices"; type: "int" }
        }
        Method {
            name: "setRadius"
            Parameter { name: "radius"; type: "float" }
        }
        Method {
            name: "setLength"
            Parameter { name: "length"; type: "float" }
        }
    }
    Component {
        name: "Qt3DExtras::QDiffuseMapMaterial"
        prototype: "Qt3DRender::QMaterial"
        exports: ["Qt3D.Extras/DiffuseMapMaterial 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "ambient"; type: "QColor" }
        Property { name: "specular"; type: "QColor" }
        Property { name: "shininess"; type: "float" }
        Property { name: "diffuse"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        Property { name: "textureScale"; type: "float" }
        Signal {
            name: "ambientChanged"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Signal {
            name: "diffuseChanged"
            Parameter { name: "diffuse"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "specularChanged"
            Parameter { name: "specular"; type: "QColor" }
        }
        Signal {
            name: "shininessChanged"
            Parameter { name: "shininess"; type: "float" }
        }
        Signal {
            name: "textureScaleChanged"
            Parameter { name: "textureScale"; type: "float" }
        }
        Method {
            name: "setAmbient"
            Parameter { name: "color"; type: "QColor" }
        }
        Method {
            name: "setSpecular"
            Parameter { name: "specular"; type: "QColor" }
        }
        Method {
            name: "setShininess"
            Parameter { name: "shininess"; type: "float" }
        }
        Method {
            name: "setDiffuse"
            Parameter { name: "diffuse"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setTextureScale"
            Parameter { name: "textureScale"; type: "float" }
        }
    }
    Component {
        name: "Qt3DExtras::QDiffuseSpecularMapMaterial"
        prototype: "Qt3DRender::QMaterial"
        exports: ["Qt3D.Extras/DiffuseSpecularMapMaterial 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "ambient"; type: "QColor" }
        Property { name: "shininess"; type: "float" }
        Property { name: "specular"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        Property { name: "diffuse"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        Property { name: "textureScale"; type: "float" }
        Signal {
            name: "ambientChanged"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Signal {
            name: "diffuseChanged"
            Parameter { name: "diffuse"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "specularChanged"
            Parameter { name: "specular"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "shininessChanged"
            Parameter { name: "shininess"; type: "float" }
        }
        Signal {
            name: "textureScaleChanged"
            Parameter { name: "textureScale"; type: "float" }
        }
        Method {
            name: "setAmbient"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Method {
            name: "setDiffuse"
            Parameter { name: "diffuse"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setSpecular"
            Parameter { name: "specular"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setShininess"
            Parameter { name: "shininess"; type: "float" }
        }
        Method {
            name: "setTextureScale"
            Parameter { name: "textureScale"; type: "float" }
        }
    }
    Component {
        name: "Qt3DExtras::QDiffuseSpecularMaterial"
        prototype: "Qt3DRender::QMaterial"
        exports: ["Qt3D.Extras/DiffuseSpecularMaterial 2.10"]
        exportMetaObjectRevisions: [0]
        Property { name: "ambient"; type: "QColor" }
        Property { name: "diffuse"; type: "QVariant" }
        Property { name: "specular"; type: "QVariant" }
        Property { name: "shininess"; type: "float" }
        Property { name: "normal"; type: "QVariant" }
        Property { name: "textureScale"; type: "float" }
        Property { name: "alphaBlending"; type: "bool" }
        Signal {
            name: "ambientChanged"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Signal {
            name: "diffuseChanged"
            Parameter { name: "diffuse"; type: "QVariant" }
        }
        Signal {
            name: "specularChanged"
            Parameter { name: "specular"; type: "QVariant" }
        }
        Signal {
            name: "shininessChanged"
            Parameter { name: "shininess"; type: "float" }
        }
        Signal {
            name: "normalChanged"
            Parameter { name: "normal"; type: "QVariant" }
        }
        Signal {
            name: "textureScaleChanged"
            Parameter { name: "textureScale"; type: "float" }
        }
        Signal {
            name: "alphaBlendingEnabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setAmbient"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Method {
            name: "setDiffuse"
            Parameter { name: "diffuse"; type: "QVariant" }
        }
        Method {
            name: "setSpecular"
            Parameter { name: "specular"; type: "QVariant" }
        }
        Method {
            name: "setShininess"
            Parameter { name: "shininess"; type: "float" }
        }
        Method {
            name: "setNormal"
            Parameter { name: "normal"; type: "QVariant" }
        }
        Method {
            name: "setTextureScale"
            Parameter { name: "textureScale"; type: "float" }
        }
        Method {
            name: "setAlphaBlendingEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
    }
    Component {
        name: "Qt3DExtras::QExtrudedTextGeometry"
        prototype: "Qt3DRender::QGeometry"
        exports: ["Qt3D.Extras/ExtrudedTextGeometry 2.9"]
        exportMetaObjectRevisions: [0]
        Property { name: "text"; type: "string" }
        Property { name: "font"; type: "QFont" }
        Property { name: "extrusionLength"; type: "float" }
        Property {
            name: "positionAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "normalAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "indexAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Signal {
            name: "textChanged"
            Parameter { name: "text"; type: "string" }
        }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "depthChanged"
            Parameter { name: "extrusionLength"; type: "float" }
        }
        Method {
            name: "setText"
            Parameter { name: "text"; type: "string" }
        }
        Method {
            name: "setFont"
            Parameter { name: "font"; type: "QFont" }
        }
        Method {
            name: "setDepth"
            Parameter { name: "extrusionLength"; type: "float" }
        }
    }
    Component {
        name: "Qt3DExtras::QExtrudedTextMesh"
        prototype: "Qt3DRender::QGeometryRenderer"
        exports: ["Qt3D.Extras/ExtrudedTextMesh 2.9"]
        exportMetaObjectRevisions: [0]
        Property { name: "text"; type: "string" }
        Property { name: "font"; type: "QFont" }
        Property { name: "depth"; type: "float" }
        Signal {
            name: "textChanged"
            Parameter { name: "text"; type: "string" }
        }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "depthChanged"
            Parameter { name: "depth"; type: "float" }
        }
        Method {
            name: "setText"
            Parameter { name: "text"; type: "string" }
        }
        Method {
            name: "setFont"
            Parameter { name: "font"; type: "QFont" }
        }
        Method {
            name: "setDepth"
            Parameter { name: "depth"; type: "float" }
        }
    }
    Component {
        name: "Qt3DExtras::QFirstPersonCameraController"
        prototype: "Qt3DExtras::QAbstractCameraController"
        exports: ["Qt3D.Extras/FirstPersonCameraController 2.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "Qt3DExtras::QForwardRenderer"
        prototype: "Qt3DRender::QTechniqueFilter"
        exports: [
            "Qt3D.Extras/ForwardRenderer 2.0",
            "Qt3D.Extras/ForwardRenderer 2.14",
            "Qt3D.Extras/ForwardRenderer 2.15",
            "Qt3D.Extras/ForwardRenderer 2.9"
        ]
        exportMetaObjectRevisions: [0, 14, 15, 9]
        Property { name: "surface"; type: "QObject"; isPointer: true }
        Property { name: "window"; type: "QObject"; isPointer: true }
        Property { name: "viewportRect"; type: "QRectF" }
        Property { name: "clearColor"; type: "QColor" }
        Property { name: "buffersToClear"; revision: 14; type: "Qt3DRender::QClearBuffers::BufferType" }
        Property { name: "camera"; type: "Qt3DCore::QEntity"; isPointer: true }
        Property { name: "externalRenderTargetSize"; type: "QSize" }
        Property { name: "frustumCulling"; type: "bool" }
        Property { name: "gamma"; revision: 9; type: "float" }
        Property { name: "showDebugOverlay"; revision: 15; type: "bool" }
        Signal {
            name: "viewportRectChanged"
            Parameter { name: "viewportRect"; type: "QRectF" }
        }
        Signal {
            name: "clearColorChanged"
            Parameter { name: "clearColor"; type: "QColor" }
        }
        Signal {
            name: "buffersToClearChanged"
            Parameter { type: "Qt3DRender::QClearBuffers::BufferType" }
        }
        Signal {
            name: "cameraChanged"
            Parameter { name: "camera"; type: "Qt3DCore::QEntity"; isPointer: true }
        }
        Signal {
            name: "surfaceChanged"
            Parameter { name: "surface"; type: "QObject"; isPointer: true }
        }
        Signal {
            name: "externalRenderTargetSizeChanged"
            Parameter { name: "size"; type: "QSize" }
        }
        Signal {
            name: "frustumCullingEnabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "gammaChanged"
            Parameter { name: "gamma"; type: "float" }
        }
        Signal {
            name: "showDebugOverlayChanged"
            Parameter { name: "showDebugOverlay"; type: "bool" }
        }
        Method {
            name: "setViewportRect"
            Parameter { name: "viewportRect"; type: "QRectF" }
        }
        Method {
            name: "setClearColor"
            Parameter { name: "clearColor"; type: "QColor" }
        }
        Method {
            name: "setBuffersToClear"
            Parameter { type: "Qt3DRender::QClearBuffers::BufferType" }
        }
        Method {
            name: "setCamera"
            Parameter { name: "camera"; type: "Qt3DCore::QEntity"; isPointer: true }
        }
        Method {
            name: "setSurface"
            Parameter { name: "surface"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "setExternalRenderTargetSize"
            Parameter { name: "size"; type: "QSize" }
        }
        Method {
            name: "setFrustumCullingEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setGamma"
            Parameter { name: "gamma"; type: "float" }
        }
        Method {
            name: "setShowDebugOverlay"
            Parameter { name: "showDebugOverlay"; type: "bool" }
        }
    }
    Component {
        name: "Qt3DExtras::QGoochMaterial"
        prototype: "Qt3DRender::QMaterial"
        exports: ["Qt3D.Extras/GoochMaterial 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "diffuse"; type: "QColor" }
        Property { name: "specular"; type: "QColor" }
        Property { name: "cool"; type: "QColor" }
        Property { name: "warm"; type: "QColor" }
        Property { name: "alpha"; type: "float" }
        Property { name: "beta"; type: "float" }
        Property { name: "shininess"; type: "float" }
        Signal {
            name: "diffuseChanged"
            Parameter { name: "diffuse"; type: "QColor" }
        }
        Signal {
            name: "specularChanged"
            Parameter { name: "specular"; type: "QColor" }
        }
        Signal {
            name: "coolChanged"
            Parameter { name: "cool"; type: "QColor" }
        }
        Signal {
            name: "warmChanged"
            Parameter { name: "warm"; type: "QColor" }
        }
        Signal {
            name: "alphaChanged"
            Parameter { name: "alpha"; type: "float" }
        }
        Signal {
            name: "betaChanged"
            Parameter { name: "beta"; type: "float" }
        }
        Signal {
            name: "shininessChanged"
            Parameter { name: "shininess"; type: "float" }
        }
        Method {
            name: "setDiffuse"
            Parameter { name: "diffuse"; type: "QColor" }
        }
        Method {
            name: "setSpecular"
            Parameter { name: "specular"; type: "QColor" }
        }
        Method {
            name: "setCool"
            Parameter { name: "cool"; type: "QColor" }
        }
        Method {
            name: "setWarm"
            Parameter { name: "warm"; type: "QColor" }
        }
        Method {
            name: "setAlpha"
            Parameter { name: "alpha"; type: "float" }
        }
        Method {
            name: "setBeta"
            Parameter { name: "beta"; type: "float" }
        }
        Method {
            name: "setShininess"
            Parameter { name: "shininess"; type: "float" }
        }
    }
    Component {
        name: "Qt3DExtras::QMetalRoughMaterial"
        prototype: "Qt3DRender::QMaterial"
        exports: [
            "Qt3D.Extras/MetalRoughMaterial 2.10",
            "Qt3D.Extras/MetalRoughMaterial 2.9"
        ]
        exportMetaObjectRevisions: [10, 0]
        Property { name: "baseColor"; type: "QVariant" }
        Property { name: "metalness"; type: "QVariant" }
        Property { name: "roughness"; type: "QVariant" }
        Property { name: "ambientOcclusion"; revision: 10; type: "QVariant" }
        Property { name: "normal"; revision: 10; type: "QVariant" }
        Property { name: "textureScale"; revision: 10; type: "float" }
        Signal {
            name: "baseColorChanged"
            Parameter { name: "baseColor"; type: "QVariant" }
        }
        Signal {
            name: "metalnessChanged"
            Parameter { name: "metalness"; type: "QVariant" }
        }
        Signal {
            name: "roughnessChanged"
            Parameter { name: "roughness"; type: "QVariant" }
        }
        Signal {
            name: "ambientOcclusionChanged"
            Parameter { name: "ambientOcclusion"; type: "QVariant" }
        }
        Signal {
            name: "normalChanged"
            Parameter { name: "normal"; type: "QVariant" }
        }
        Signal {
            name: "textureScaleChanged"
            Parameter { name: "textureScale"; type: "float" }
        }
        Method {
            name: "setBaseColor"
            Parameter { name: "baseColor"; type: "QVariant" }
        }
        Method {
            name: "setMetalness"
            Parameter { name: "metalness"; type: "QVariant" }
        }
        Method {
            name: "setRoughness"
            Parameter { name: "roughness"; type: "QVariant" }
        }
        Method {
            name: "setAmbientOcclusion"
            Parameter { name: "ambientOcclusion"; type: "QVariant" }
        }
        Method {
            name: "setNormal"
            Parameter { name: "normal"; type: "QVariant" }
        }
        Method {
            name: "setTextureScale"
            Parameter { name: "textureScale"; type: "float" }
        }
    }
    Component {
        name: "Qt3DExtras::QMorphPhongMaterial"
        prototype: "Qt3DRender::QMaterial"
        exports: ["Qt3D.Extras/MorphPhongMaterial 2.9"]
        exportMetaObjectRevisions: [0]
        Property { name: "ambient"; type: "QColor" }
        Property { name: "diffuse"; type: "QColor" }
        Property { name: "specular"; type: "QColor" }
        Property { name: "shininess"; type: "float" }
        Property { name: "interpolator"; type: "float" }
        Signal {
            name: "ambientChanged"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Signal {
            name: "diffuseChanged"
            Parameter { name: "diffuse"; type: "QColor" }
        }
        Signal {
            name: "specularChanged"
            Parameter { name: "specular"; type: "QColor" }
        }
        Signal {
            name: "shininessChanged"
            Parameter { name: "shininess"; type: "float" }
        }
        Signal {
            name: "interpolatorChanged"
            Parameter { name: "interpolator"; type: "float" }
        }
        Method {
            name: "setAmbient"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Method {
            name: "setDiffuse"
            Parameter { name: "diffuse"; type: "QColor" }
        }
        Method {
            name: "setSpecular"
            Parameter { name: "specular"; type: "QColor" }
        }
        Method {
            name: "setShininess"
            Parameter { name: "shininess"; type: "float" }
        }
        Method {
            name: "setInterpolator"
            Parameter { name: "interpolator"; type: "float" }
        }
    }
    Component {
        name: "Qt3DExtras::QNormalDiffuseMapAlphaMaterial"
        prototype: "Qt3DExtras::QNormalDiffuseMapMaterial"
        exports: ["Qt3D.Extras/NormalDiffuseMapAlphaMaterial 2.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "Qt3DExtras::QNormalDiffuseMapMaterial"
        prototype: "Qt3DRender::QMaterial"
        exports: ["Qt3D.Extras/NormalDiffuseMapMaterial 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "ambient"; type: "QColor" }
        Property { name: "specular"; type: "QColor" }
        Property { name: "diffuse"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        Property { name: "normal"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        Property { name: "shininess"; type: "float" }
        Property { name: "textureScale"; type: "float" }
        Signal {
            name: "ambientChanged"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Signal {
            name: "diffuseChanged"
            Parameter { name: "diffuse"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "normalChanged"
            Parameter { name: "normal"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "specularChanged"
            Parameter { name: "specular"; type: "QColor" }
        }
        Signal {
            name: "shininessChanged"
            Parameter { name: "shininess"; type: "float" }
        }
        Signal {
            name: "textureScaleChanged"
            Parameter { name: "textureScale"; type: "float" }
        }
        Method {
            name: "setAmbient"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Method {
            name: "setSpecular"
            Parameter { name: "specular"; type: "QColor" }
        }
        Method {
            name: "setDiffuse"
            Parameter { name: "diffuse"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setNormal"
            Parameter { name: "normal"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setShininess"
            Parameter { name: "shininess"; type: "float" }
        }
        Method {
            name: "setTextureScale"
            Parameter { name: "textureScale"; type: "float" }
        }
    }
    Component {
        name: "Qt3DExtras::QNormalDiffuseSpecularMapMaterial"
        prototype: "Qt3DRender::QMaterial"
        exports: ["Qt3D.Extras/NormalDiffuseSpecularMapMaterial 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "ambient"; type: "QColor" }
        Property { name: "diffuse"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        Property { name: "normal"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        Property { name: "specular"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        Property { name: "shininess"; type: "float" }
        Property { name: "textureScale"; type: "float" }
        Signal {
            name: "ambientChanged"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Signal {
            name: "diffuseChanged"
            Parameter { name: "diffuse"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "normalChanged"
            Parameter { name: "normal"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "specularChanged"
            Parameter { name: "specular"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "shininessChanged"
            Parameter { name: "shininess"; type: "float" }
        }
        Signal {
            name: "textureScaleChanged"
            Parameter { name: "textureScale"; type: "float" }
        }
        Method {
            name: "setAmbient"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Method {
            name: "setDiffuse"
            Parameter { name: "diffuse"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setNormal"
            Parameter { name: "normal"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setSpecular"
            Parameter { name: "specular"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setShininess"
            Parameter { name: "shininess"; type: "float" }
        }
        Method {
            name: "setTextureScale"
            Parameter { name: "textureScale"; type: "float" }
        }
    }
    Component {
        name: "Qt3DExtras::QOrbitCameraController"
        prototype: "Qt3DExtras::QAbstractCameraController"
        exports: ["Qt3D.Extras/OrbitCameraController 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "zoomInLimit"; type: "float" }
    }
    Component {
        name: "Qt3DExtras::QPerVertexColorMaterial"
        prototype: "Qt3DRender::QMaterial"
        exports: ["Qt3D.Extras/PerVertexColorMaterial 2.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        name: "Qt3DExtras::QPhongAlphaMaterial"
        prototype: "Qt3DRender::QMaterial"
        exports: ["Qt3D.Extras/PhongAlphaMaterial 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "ambient"; type: "QColor" }
        Property { name: "diffuse"; type: "QColor" }
        Property { name: "specular"; type: "QColor" }
        Property { name: "shininess"; type: "float" }
        Property { name: "alpha"; type: "float" }
        Property { name: "sourceRgbArg"; type: "Qt3DRender::QBlendEquationArguments::Blending" }
        Property { name: "destinationRgbArg"; type: "Qt3DRender::QBlendEquationArguments::Blending" }
        Property { name: "sourceAlphaArg"; type: "Qt3DRender::QBlendEquationArguments::Blending" }
        Property {
            name: "destinationAlphaArg"
            type: "Qt3DRender::QBlendEquationArguments::Blending"
        }
        Property { name: "blendFunctionArg"; type: "Qt3DRender::QBlendEquation::BlendFunction" }
        Signal {
            name: "ambientChanged"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Signal {
            name: "diffuseChanged"
            Parameter { name: "diffuse"; type: "QColor" }
        }
        Signal {
            name: "specularChanged"
            Parameter { name: "specular"; type: "QColor" }
        }
        Signal {
            name: "shininessChanged"
            Parameter { name: "shininess"; type: "float" }
        }
        Signal {
            name: "alphaChanged"
            Parameter { name: "alpha"; type: "float" }
        }
        Signal {
            name: "sourceRgbArgChanged"
            Parameter { name: "sourceRgbArg"; type: "Qt3DRender::QBlendEquationArguments::Blending" }
        }
        Signal {
            name: "destinationRgbArgChanged"
            Parameter { name: "destinationRgbArg"; type: "Qt3DRender::QBlendEquationArguments::Blending" }
        }
        Signal {
            name: "sourceAlphaArgChanged"
            Parameter { name: "sourceAlphaArg"; type: "Qt3DRender::QBlendEquationArguments::Blending" }
        }
        Signal {
            name: "destinationAlphaArgChanged"
            Parameter {
                name: "destinationAlphaArg"
                type: "Qt3DRender::QBlendEquationArguments::Blending"
            }
        }
        Signal {
            name: "blendFunctionArgChanged"
            Parameter { name: "blendFunctionArg"; type: "Qt3DRender::QBlendEquation::BlendFunction" }
        }
        Method {
            name: "setAmbient"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Method {
            name: "setDiffuse"
            Parameter { name: "diffuse"; type: "QColor" }
        }
        Method {
            name: "setSpecular"
            Parameter { name: "specular"; type: "QColor" }
        }
        Method {
            name: "setShininess"
            Parameter { name: "shininess"; type: "float" }
        }
        Method {
            name: "setAlpha"
            Parameter { name: "alpha"; type: "float" }
        }
        Method {
            name: "setSourceRgbArg"
            Parameter { name: "sourceRgbArg"; type: "Qt3DRender::QBlendEquationArguments::Blending" }
        }
        Method {
            name: "setDestinationRgbArg"
            Parameter { name: "destinationRgbArg"; type: "Qt3DRender::QBlendEquationArguments::Blending" }
        }
        Method {
            name: "setSourceAlphaArg"
            Parameter { name: "sourceAlphaArg"; type: "Qt3DRender::QBlendEquationArguments::Blending" }
        }
        Method {
            name: "setDestinationAlphaArg"
            Parameter {
                name: "destinationAlphaArg"
                type: "Qt3DRender::QBlendEquationArguments::Blending"
            }
        }
        Method {
            name: "setBlendFunctionArg"
            Parameter { name: "blendFunctionArg"; type: "Qt3DRender::QBlendEquation::BlendFunction" }
        }
    }
    Component {
        name: "Qt3DExtras::QPhongMaterial"
        prototype: "Qt3DRender::QMaterial"
        exports: ["Qt3D.Extras/PhongMaterial 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "ambient"; type: "QColor" }
        Property { name: "diffuse"; type: "QColor" }
        Property { name: "specular"; type: "QColor" }
        Property { name: "shininess"; type: "float" }
        Signal {
            name: "ambientChanged"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Signal {
            name: "diffuseChanged"
            Parameter { name: "diffuse"; type: "QColor" }
        }
        Signal {
            name: "specularChanged"
            Parameter { name: "specular"; type: "QColor" }
        }
        Signal {
            name: "shininessChanged"
            Parameter { name: "shininess"; type: "float" }
        }
        Method {
            name: "setAmbient"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Method {
            name: "setDiffuse"
            Parameter { name: "diffuse"; type: "QColor" }
        }
        Method {
            name: "setSpecular"
            Parameter { name: "specular"; type: "QColor" }
        }
        Method {
            name: "setShininess"
            Parameter { name: "shininess"; type: "float" }
        }
    }
    Component {
        name: "Qt3DExtras::QPlaneGeometry"
        prototype: "Qt3DRender::QGeometry"
        exports: [
            "Qt3D.Extras/PlaneGeometry 2.0",
            "Qt3D.Extras/PlaneGeometry 2.9"
        ]
        exportMetaObjectRevisions: [0, 9]
        Property { name: "width"; type: "float" }
        Property { name: "height"; type: "float" }
        Property { name: "resolution"; type: "QSize" }
        Property { name: "mirrored"; revision: 9; type: "bool" }
        Property {
            name: "positionAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "normalAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "texCoordAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "tangentAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "indexAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Signal {
            name: "resolutionChanged"
            Parameter { name: "resolution"; type: "QSize" }
        }
        Signal {
            name: "widthChanged"
            Parameter { name: "width"; type: "float" }
        }
        Signal {
            name: "heightChanged"
            Parameter { name: "height"; type: "float" }
        }
        Signal {
            name: "mirroredChanged"
            Parameter { name: "mirrored"; type: "bool" }
        }
        Method {
            name: "setResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
        Method {
            name: "setWidth"
            Parameter { name: "width"; type: "float" }
        }
        Method {
            name: "setHeight"
            Parameter { name: "height"; type: "float" }
        }
        Method {
            name: "setMirrored"
            Parameter { name: "mirrored"; type: "bool" }
        }
    }
    Component {
        name: "Qt3DExtras::QPlaneMesh"
        prototype: "Qt3DRender::QGeometryRenderer"
        exports: ["Qt3D.Extras/PlaneMesh 2.0", "Qt3D.Extras/PlaneMesh 2.9"]
        exportMetaObjectRevisions: [0, 9]
        Property { name: "width"; type: "float" }
        Property { name: "height"; type: "float" }
        Property { name: "meshResolution"; type: "QSize" }
        Property { name: "mirrored"; revision: 9; type: "bool" }
        Signal {
            name: "meshResolutionChanged"
            Parameter { name: "meshResolution"; type: "QSize" }
        }
        Signal {
            name: "widthChanged"
            Parameter { name: "width"; type: "float" }
        }
        Signal {
            name: "heightChanged"
            Parameter { name: "height"; type: "float" }
        }
        Signal {
            name: "mirroredChanged"
            Parameter { name: "mirrored"; type: "bool" }
        }
        Method {
            name: "setWidth"
            Parameter { name: "width"; type: "float" }
        }
        Method {
            name: "setHeight"
            Parameter { name: "height"; type: "float" }
        }
        Method {
            name: "setMeshResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
        Method {
            name: "setMirrored"
            Parameter { name: "mirrored"; type: "bool" }
        }
    }
    Component {
        name: "Qt3DExtras::QSkyboxEntity"
        prototype: "Qt3DCore::QEntity"
        exports: [
            "Qt3D.Extras/SkyboxEntity 2.0",
            "Qt3D.Extras/SkyboxEntity 2.9"
        ]
        exportMetaObjectRevisions: [0, 9]
        Property { name: "baseName"; type: "string" }
        Property { name: "extension"; type: "string" }
        Property { name: "gammaCorrect"; revision: 9; type: "bool" }
        Signal {
            name: "baseNameChanged"
            Parameter { name: "path"; type: "string" }
        }
        Signal {
            name: "extensionChanged"
            Parameter { name: "extension"; type: "string" }
        }
        Signal {
            name: "gammaCorrectEnabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setBaseName"
            Parameter { name: "path"; type: "string" }
        }
        Method {
            name: "setExtension"
            Parameter { name: "extension"; type: "string" }
        }
        Method {
            name: "setGammaCorrectEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
    }
    Component {
        name: "Qt3DExtras::QSphereGeometry"
        prototype: "Qt3DRender::QGeometry"
        exports: ["Qt3D.Extras/SphereGeometry 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "rings"; type: "int" }
        Property { name: "slices"; type: "int" }
        Property { name: "radius"; type: "float" }
        Property { name: "generateTangents"; type: "bool" }
        Property {
            name: "positionAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "normalAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "texCoordAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "tangentAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "indexAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Signal {
            name: "radiusChanged"
            Parameter { name: "radius"; type: "float" }
        }
        Signal {
            name: "ringsChanged"
            Parameter { name: "rings"; type: "int" }
        }
        Signal {
            name: "slicesChanged"
            Parameter { name: "slices"; type: "int" }
        }
        Signal {
            name: "generateTangentsChanged"
            Parameter { name: "generateTangents"; type: "bool" }
        }
        Method {
            name: "setRings"
            Parameter { name: "rings"; type: "int" }
        }
        Method {
            name: "setSlices"
            Parameter { name: "slices"; type: "int" }
        }
        Method {
            name: "setRadius"
            Parameter { name: "radius"; type: "float" }
        }
        Method {
            name: "setGenerateTangents"
            Parameter { name: "gen"; type: "bool" }
        }
    }
    Component {
        name: "Qt3DExtras::QSphereMesh"
        prototype: "Qt3DRender::QGeometryRenderer"
        exports: ["Qt3D.Extras/SphereMesh 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "rings"; type: "int" }
        Property { name: "slices"; type: "int" }
        Property { name: "radius"; type: "float" }
        Property { name: "generateTangents"; type: "bool" }
        Signal {
            name: "radiusChanged"
            Parameter { name: "radius"; type: "float" }
        }
        Signal {
            name: "ringsChanged"
            Parameter { name: "rings"; type: "int" }
        }
        Signal {
            name: "slicesChanged"
            Parameter { name: "slices"; type: "int" }
        }
        Signal {
            name: "generateTangentsChanged"
            Parameter { name: "generateTangents"; type: "bool" }
        }
        Method {
            name: "setRings"
            Parameter { name: "rings"; type: "int" }
        }
        Method {
            name: "setSlices"
            Parameter { name: "slices"; type: "int" }
        }
        Method {
            name: "setRadius"
            Parameter { name: "radius"; type: "float" }
        }
        Method {
            name: "setGenerateTangents"
            Parameter { name: "gen"; type: "bool" }
        }
    }
    Component {
        name: "Qt3DExtras::QSpriteGrid"
        prototype: "Qt3DExtras::QAbstractSpriteSheet"
        exports: ["Qt3D.Extras/SpriteGrid 2.10"]
        exportMetaObjectRevisions: [0]
        Property { name: "rows"; type: "int" }
        Property { name: "columns"; type: "int" }
        Signal {
            name: "rowsChanged"
            Parameter { name: "rows"; type: "int" }
        }
        Signal {
            name: "columnsChanged"
            Parameter { name: "columns"; type: "int" }
        }
        Method {
            name: "setRows"
            Parameter { name: "rows"; type: "int" }
        }
        Method {
            name: "setColumns"
            Parameter { name: "columns"; type: "int" }
        }
    }
    Component {
        name: "Qt3DExtras::QSpriteSheet"
        defaultProperty: "sprites"
        prototype: "Qt3DExtras::QAbstractSpriteSheet"
        exports: ["Qt3D.Extras/SpriteSheet 2.10"]
        exportMetaObjectRevisions: [210]
        Property { name: "sprites"; type: "QVector<QSpriteSheetItem*>" }
        Signal {
            name: "spritesChanged"
            Parameter { name: "sprites"; type: "QVector<QSpriteSheetItem*>" }
        }
        Method {
            name: "setSprites"
            Parameter { name: "sprites"; type: "QVector<QSpriteSheetItem*>" }
        }
        Property {
            name: "sprites"
            revision: 210
            type: "Qt3DExtras::QSpriteSheetItem"
            isList: true
            isReadonly: true
        }
    }
    Component {
        name: "Qt3DExtras::QSpriteSheetItem"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Extras/SpriteItem 2.10"]
        exportMetaObjectRevisions: [0]
        Property { name: "x"; type: "int" }
        Property { name: "y"; type: "int" }
        Property { name: "width"; type: "int" }
        Property { name: "height"; type: "int" }
        Signal {
            name: "xChanged"
            Parameter { name: "x"; type: "int" }
        }
        Signal {
            name: "yChanged"
            Parameter { name: "y"; type: "int" }
        }
        Signal {
            name: "widthChanged"
            Parameter { name: "width"; type: "int" }
        }
        Signal {
            name: "heightChanged"
            Parameter { name: "height"; type: "int" }
        }
        Method {
            name: "setX"
            Parameter { name: "x"; type: "int" }
        }
        Method {
            name: "setY"
            Parameter { name: "y"; type: "int" }
        }
        Method {
            name: "setWidth"
            Parameter { name: "width"; type: "int" }
        }
        Method {
            name: "setHeight"
            Parameter { name: "height"; type: "int" }
        }
    }
    Component {
        name: "Qt3DExtras::QText2DEntity"
        prototype: "Qt3DCore::QEntity"
        exports: ["Qt3D.Extras/Text2DEntity 2.9"]
        exportMetaObjectRevisions: [0]
        Property { name: "font"; type: "QFont" }
        Property { name: "text"; type: "string" }
        Property { name: "color"; type: "QColor" }
        Property { name: "width"; type: "float" }
        Property { name: "height"; type: "float" }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "textChanged"
            Parameter { name: "text"; type: "string" }
        }
        Signal {
            name: "widthChanged"
            Parameter { name: "width"; type: "float" }
        }
        Signal {
            name: "heightChanged"
            Parameter { name: "height"; type: "float" }
        }
    }
    Component {
        name: "Qt3DExtras::QTextureMaterial"
        prototype: "Qt3DRender::QMaterial"
        exports: [
            "Qt3D.Extras/TextureMaterial 2.0",
            "Qt3D.Extras/TextureMaterial 2.10",
            "Qt3D.Extras/TextureMaterial 2.11"
        ]
        exportMetaObjectRevisions: [0, 10, 11]
        Property { name: "texture"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        Property { name: "textureOffset"; type: "QVector2D" }
        Property { name: "textureTransform"; revision: 10; type: "QMatrix3x3" }
        Property { name: "alphaBlending"; revision: 11; type: "bool" }
        Signal {
            name: "textureChanged"
            Parameter { name: "texture"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "textureOffsetChanged"
            Parameter { name: "textureOffset"; type: "QVector2D" }
        }
        Signal {
            name: "textureTransformChanged"
            Parameter { name: "textureTransform"; type: "QMatrix3x3" }
        }
        Signal {
            name: "alphaBlendingEnabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setTexture"
            Parameter { name: "texture"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setTextureOffset"
            Parameter { name: "textureOffset"; type: "QVector2D" }
        }
        Method {
            name: "setTextureTransform"
            Parameter { name: "matrix"; type: "QMatrix3x3" }
        }
        Method {
            name: "setAlphaBlendingEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
    }
    Component {
        name: "Qt3DExtras::QTorusGeometry"
        prototype: "Qt3DRender::QGeometry"
        exports: ["Qt3D.Extras/TorusGeometry 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "rings"; type: "int" }
        Property { name: "slices"; type: "int" }
        Property { name: "radius"; type: "float" }
        Property { name: "minorRadius"; type: "float" }
        Property {
            name: "positionAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "normalAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "texCoordAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Property {
            name: "indexAttribute"
            type: "Qt3DRender::QAttribute"
            isReadonly: true
            isPointer: true
        }
        Signal {
            name: "radiusChanged"
            Parameter { name: "radius"; type: "float" }
        }
        Signal {
            name: "ringsChanged"
            Parameter { name: "rings"; type: "int" }
        }
        Signal {
            name: "slicesChanged"
            Parameter { name: "slices"; type: "int" }
        }
        Signal {
            name: "minorRadiusChanged"
            Parameter { name: "minorRadius"; type: "float" }
        }
        Method {
            name: "setRings"
            Parameter { name: "rings"; type: "int" }
        }
        Method {
            name: "setSlices"
            Parameter { name: "slices"; type: "int" }
        }
        Method {
            name: "setRadius"
            Parameter { name: "radius"; type: "float" }
        }
        Method {
            name: "setMinorRadius"
            Parameter { name: "minorRadius"; type: "float" }
        }
    }
    Component {
        name: "Qt3DExtras::QTorusMesh"
        prototype: "Qt3DRender::QGeometryRenderer"
        exports: ["Qt3D.Extras/TorusMesh 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "rings"; type: "int" }
        Property { name: "slices"; type: "int" }
        Property { name: "radius"; type: "float" }
        Property { name: "minorRadius"; type: "float" }
        Signal {
            name: "radiusChanged"
            Parameter { name: "radius"; type: "float" }
        }
        Signal {
            name: "ringsChanged"
            Parameter { name: "rings"; type: "int" }
        }
        Signal {
            name: "slicesChanged"
            Parameter { name: "slices"; type: "int" }
        }
        Signal {
            name: "minorRadiusChanged"
            Parameter { name: "minorRadius"; type: "float" }
        }
        Method {
            name: "setRings"
            Parameter { name: "rings"; type: "int" }
        }
        Method {
            name: "setSlices"
            Parameter { name: "slices"; type: "int" }
        }
        Method {
            name: "setRadius"
            Parameter { name: "radius"; type: "float" }
        }
        Method {
            name: "setMinorRadius"
            Parameter { name: "minorRadius"; type: "float" }
        }
    }
}
