import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquickviewcontroller_p.h"
        name: "QQuickViewController"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        Method {
            name: "onWindowChanged"
            Parameter { name: "window"; type: "QQuickWindow"; isPointer: true }
        }
        Method { name: "onVisibleChanged" }
        Method { name: "scheduleUpdatePolish" }
        Method { name: "onSceneGraphInvalidated" }
    }
    Component {
        file: "private/qquickwebview_p.h"
        name: "QQuickWebView"
        accessSemantics: "reference"
        prototype: "QQuickViewController"
        exports: [
            "QtWebView/WebView 1.0",
            "QtWebView/WebView 1.1",
            "QtWebView/WebView 1.14",
            "QtWebView/WebView 2.0",
            "QtWebView/WebView 2.1",
            "QtWebView/WebView 2.4",
            "QtWebView/WebView 2.7",
            "QtWebView/WebView 2.11",
            "QtWebView/WebView 6.0",
            "QtWebView/WebView 6.3",
            "QtWebView/WebView 6.5",
            "QtWebView/WebView 6.7"
        ]
        exportMetaObjectRevisions: [
            256,
            257,
            270,
            512,
            513,
            516,
            519,
            523,
            1536,
            1539,
            1541,
            1543
        ]
        Enum {
            name: "LoadStatus"
            values: [
                "LoadStartedStatus",
                "LoadStoppedStatus",
                "LoadSucceededStatus",
                "LoadFailedStatus"
            ]
        }
        Property {
            name: "httpUserAgent"
            revision: 270
            type: "QString"
            read: "httpUserAgent"
            write: "setHttpUserAgent"
            notify: "httpUserAgentChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "url"
            type: "QUrl"
            read: "url"
            write: "setUrl"
            notify: "urlChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "loading"
            revision: 257
            type: "bool"
            read: "isLoading"
            notify: "loadingChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "loadProgress"
            type: "int"
            read: "loadProgress"
            notify: "loadProgressChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "title"
            type: "QString"
            read: "title"
            notify: "titleChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "canGoBack"
            type: "bool"
            read: "canGoBack"
            notify: "loadingChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "canGoForward"
            type: "bool"
            read: "canGoForward"
            notify: "loadingChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "settings"
            revision: 1541
            type: "QQuickWebViewSettings"
            isPointer: true
            read: "settings"
            index: 7
            isReadonly: true
            isFinal: true
            isConstant: true
        }
        Signal { name: "titleChanged" }
        Signal { name: "urlChanged" }
        Signal {
            name: "loadingChanged"
            revision: 257
            Parameter { name: "loadRequest"; type: "QQuickWebViewLoadRequest"; isPointer: true }
        }
        Signal { name: "loadProgressChanged" }
        Signal { name: "httpUserAgentChanged"; revision: 270 }
        Signal {
            name: "cookieAdded"
            revision: 1539
            Parameter { name: "domain"; type: "QString" }
            Parameter { name: "name"; type: "QString" }
        }
        Signal {
            name: "cookieRemoved"
            revision: 1539
            Parameter { name: "domain"; type: "QString" }
            Parameter { name: "name"; type: "QString" }
        }
        Method { name: "goBack" }
        Method { name: "goForward" }
        Method { name: "reload" }
        Method { name: "stop" }
        Method {
            name: "loadHtml"
            revision: 257
            Parameter { name: "html"; type: "QString" }
            Parameter { name: "baseUrl"; type: "QUrl" }
        }
        Method {
            name: "loadHtml"
            revision: 257
            isCloned: true
            Parameter { name: "html"; type: "QString" }
        }
        Method {
            name: "runJavaScript"
            revision: 257
            Parameter { name: "script"; type: "QString" }
            Parameter { name: "callback"; type: "QJSValue" }
        }
        Method {
            name: "runJavaScript"
            revision: 257
            isCloned: true
            Parameter { name: "script"; type: "QString" }
        }
        Method {
            name: "setCookie"
            revision: 1539
            Parameter { name: "domain"; type: "QString" }
            Parameter { name: "name"; type: "QString" }
            Parameter { name: "value"; type: "QString" }
        }
        Method {
            name: "deleteCookie"
            revision: 1539
            Parameter { name: "domain"; type: "QString" }
            Parameter { name: "name"; type: "QString" }
        }
        Method { name: "deleteAllCookies"; revision: 1539 }
        Method {
            name: "onRunJavaScriptResult"
            Parameter { name: "id"; type: "int" }
            Parameter { name: "variant"; type: "QVariant" }
        }
        Method {
            name: "onFocusRequest"
            Parameter { name: "focus"; type: "bool" }
        }
        Method {
            name: "onLoadingChanged"
            Parameter { name: "loadRequest"; type: "QWebViewLoadRequestPrivate" }
        }
    }
    Component {
        file: "private/qquickwebviewloadrequest_p.h"
        name: "QQuickWebViewLoadRequest"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtWebView/WebViewLoadRequest 1.1",
            "QtWebView/WebViewLoadRequest 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [257, 1536]
        Property { name: "url"; type: "QUrl"; read: "url"; index: 0; isReadonly: true }
        Property {
            name: "status"
            type: "QQuickWebView::LoadStatus"
            read: "status"
            index: 1
            isReadonly: true
        }
        Property { name: "errorString"; type: "QString"; read: "errorString"; index: 2; isReadonly: true }
    }
    Component {
        file: "private/qquickwebviewsettings_p.h"
        name: "QQuickWebViewSettings"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtWebView/WebViewSettings 6.5"]
        isCreatable: false
        exportMetaObjectRevisions: [1541]
        Property {
            name: "localStorageEnabled"
            type: "bool"
            read: "localStorageEnabled"
            write: "setLocalStorageEnabled"
            notify: "localStorageEnabledChanged"
            index: 0
        }
        Property {
            name: "javaScriptEnabled"
            type: "bool"
            read: "javaScriptEnabled"
            write: "setJavaScriptEnabled"
            notify: "javaScriptEnabledChanged"
            index: 1
        }
        Property {
            name: "allowFileAccess"
            type: "bool"
            read: "allowFileAccess"
            write: "setAllowFileAccess"
            notify: "allowFileAccessChanged"
            index: 2
        }
        Property {
            name: "localContentCanAccessFileUrls"
            type: "bool"
            read: "localContentCanAccessFileUrls"
            write: "setLocalContentCanAccessFileUrls"
            notify: "localContentCanAccessFileUrlsChanged"
            index: 3
        }
        Signal { name: "localStorageEnabledChanged" }
        Signal { name: "javaScriptEnabledChanged" }
        Signal { name: "allowFileAccessChanged" }
        Signal { name: "localContentCanAccessFileUrlsChanged" }
        Method {
            name: "setLocalStorageEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setJavaScriptEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setAllowFileAccess"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setLocalContentCanAccessFileUrls"
            Parameter { name: "enabled"; type: "bool" }
        }
    }
}
