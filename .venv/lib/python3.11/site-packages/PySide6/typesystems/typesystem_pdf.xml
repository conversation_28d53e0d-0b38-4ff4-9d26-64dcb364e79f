<?xml version="1.0"?>
<!--
// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtPdf"
            namespace-begin="QT_BEGIN_NAMESPACE" namespace-end="QT_END_NAMESPACE">
    <load-typesystem name="typesystem_core.xml" generate="no"/>
    <load-typesystem name="typesystem_gui.xml" generate="no"/>
    <object-type name="QPdfBookmarkModel">
        <enum-type name="Role" python-type="IntEnum"/>
    </object-type>
    <object-type name="QPdfDocument">
        <enum-type name="Status"/>
        <enum-type name="Error"/>
        <enum-type name="MetaDataField"/>
        <enum-type name="PageModelRole"/>
    </object-type>
    <value-type name="QPdfDocumentRenderOptions">
        <enum-type name="RenderFlag" flags="RenderFlags" since="6.5"/>
        <enum-type name="Rotation" since="6.5"/>
    </value-type>
    <object-type name="QPdfPageRenderer">
        <enum-type name="RenderMode"/>
    </object-type>
    <value-type name="QPdfLink"/>
    <object-type name="QPdfPageNavigator"/>
    <object-type name="QPdfSearchModel">
        <enum-type name="Role"/>
    </object-type>
    <object-type name="QPdfLinkModel" since="6.6">
        <enum-type name="Role"/>
    </object-type>
    <object-type name="QPdfSelection"/>
</typesystem>
