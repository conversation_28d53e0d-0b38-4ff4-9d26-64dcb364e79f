#!/usr/bin/env bash
set -euo pipefail

# Resolve root directory even if path contains spaces
ROOT="$(cd "$(dirname "$0")" && pwd)"
VENV_DIR="$ROOT/.venv"
PY="$VENV_DIR/bin/python"
PYPI_INDEX="${PYPI_INDEX:-https://pypi.vnpy.com}"

echo "[VeighNa] Root: $ROOT"

# Create venv if missing
if [[ ! -d "$VENV_DIR" ]]; then
  echo "[VeighNa] Creating virtual environment..."
  python3 -m venv "$VENV_DIR"
fi

echo "[VeighNa] Upgrading pip/setuptools/wheel..."
"$PY" -m pip install -U pip setuptools wheel

# Try to ensure ta-lib system library via Homebrew (no-op if not available)
if command -v brew >/dev/null 2>&1; then
  if ! brew list --versions ta-lib >/dev/null 2>&1; then
    echo "[VeighNa] Installing ta-lib via Homebrew..."
    HOMEBREW_NO_AUTO_UPDATE=1 brew install ta-lib
  fi
else
  echo "[VeighNa] Homebrew not found, skipping ta-lib system install."
fi

echo "[VeighNa] Ensuring numpy and ta-lib Python packages..."
"$PY" -m pip install -U "numpy==2.2.3" -i "$PYPI_INDEX"
"$PY" -m pip install -U "ta-lib==0.6.4" -i "$PYPI_INDEX"

# Install vnpy from local source if not present
if ! "$PY" - <<'PYCODE'
import importlib.util, sys
sys.exit(0 if importlib.util.find_spec("vnpy") else 1)
PYCODE
then
  echo "[VeighNa] Installing vnpy from local source..."
  "$PY" -m pip install -e "$ROOT/vnpy-master" -i "$PYPI_INDEX"
fi

echo "[VeighNa] vnpy version:"
"$PY" - <<'PYCODE'
import vnpy
print(vnpy.__version__)
PYCODE

echo "[VeighNa] Starting VeighNa Trader example..."
"$PY" - <<'PYCODE'
import importlib.util, os, sys

# If optional gateway/app packages exist, run the full example; otherwise run a minimal UI.
has_ctp = importlib.util.find_spec("vnpy_ctp") is not None
if has_ctp:
    # Run the original example script
    root = os.path.abspath(os.path.join(os.path.dirname(__file__), "vnpy-master", "examples", "veighna_trader"))
    sys.path.insert(0, root)
    import runpy
    runpy.run_path(os.path.join(root, "run.py"), run_name="__main__")
else:
    # Minimal UI runner without external gateways/apps
    from vnpy.event import EventEngine
    from vnpy.trader.engine import MainEngine
    from vnpy.trader.ui import MainWindow, create_qapp

    qapp = create_qapp()
    event_engine = EventEngine()
    main_engine = MainEngine(event_engine)

    main_window = MainWindow(main_engine, event_engine)
    main_window.showMaximized()
    qapp.exec()
PYCODE